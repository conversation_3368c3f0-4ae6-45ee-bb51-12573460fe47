{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\test\\MousewheelTest.vue?vue&type=style&index=0&id=4730e61b&scoped=true&lang=css", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\test\\MousewheelTest.vue", "mtime": 1754292322701}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\css-loader\\index.js", "mtime": 1754288853282}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754288971601}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754288923244}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754288946893}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.mousewheel-test {\n  padding: 20px;\n}\n\n.test-content {\n  line-height: 1.6;\n}\n\n.test-sections {\n  margin: 20px 0;\n}\n\n.test-card {\n  margin-bottom: 20px;\n}\n\n.status-info {\n  margin-top: 30px;\n  padding: 20px;\n  background-color: #f5f5f5;\n  border-radius: 4px;\n}\n\n.status-info ul {\n  list-style: none;\n  padding: 0;\n}\n\n.status-info li {\n  padding: 5px 0;\n  font-family: monospace;\n}\n", null]}