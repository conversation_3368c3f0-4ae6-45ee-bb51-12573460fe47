{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\test\\MousewheelTest.vue", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\test\\MousewheelTest.vue", "mtime": 1754292322701}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754288946893}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\eslint-loader\\index.js", "mtime": 1754288859365}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./MousewheelTest.vue?vue&type=template&id=4730e61b&scoped=true\"\nimport script from \"./MousewheelTest.vue?vue&type=script&lang=js\"\nexport * from \"./MousewheelTest.vue?vue&type=script&lang=js\"\nimport style0 from \"./MousewheelTest.vue?vue&type=style&index=0&id=4730e61b&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4730e61b\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\word\\\\ape-volo\\\\VUE2\\\\ape-volo-web\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('4730e61b')) {\n      api.createRecord('4730e61b', component.options)\n    } else {\n      api.reload('4730e61b', component.options)\n    }\n    module.hot.accept(\"./MousewheelTest.vue?vue&type=template&id=4730e61b&scoped=true\", function () {\n      api.rerender('4730e61b', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/test/MousewheelTest.vue\"\nexport default component.exports"]}