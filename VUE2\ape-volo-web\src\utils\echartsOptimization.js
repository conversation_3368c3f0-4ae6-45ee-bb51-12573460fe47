// Vue 2 + ECharts 4.x 性能优化配置

import * as echarts from 'echarts'

// ECharts 4.x 性能优化配置
export const ECHARTS_PERFORMANCE_CONFIG = {
  // 渲染器配置
  renderer: 'canvas', // ECharts 4.x 默认使用 canvas
  useDirtyRect: false, // ECharts 4.x 不支持脏矩形优化

  // 动画配置
  animation: true,
  animationThreshold: 2000, // 图形数量阈值
  animationDuration: 1000,
  animationEasing: 'cubicOut',
  animationDelay: 0,
  animationDurationUpdate: 300,
  animationEasingUpdate: 'cubicOut',
  animationDelayUpdate: 0
}

// 通用图表配置
export const COMMON_CHART_OPTIONS = {
  // 工具提示配置
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(50,50,50,0.7)',
    borderColor: '#333',
    textStyle: {
      color: '#fff'
    },
    // 性能优化：减少tooltip的渲染频率
    enterable: false,
    hideDelay: 100
  },

  // 图例配置
  legend: {
    type: 'scroll', // 使用滚动图例
    pageButtonItemGap: 5,
    pageButtonGap: 20,
    pageButtonPosition: 'end'
  },

  // 网格配置
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  }
}

// 大数据量图表优化配置
export const BIG_DATA_CHART_OPTIONS = {
  // 采样配置
  sampling: 'average', // ECharts 4.x 支持的采样方法

  // 大数据渲染优化
  large: true,
  largeThreshold: 2000,

  // 动画优化
  animation: false, // 大数据时关闭动画

  // 工具提示优化
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    },
    // 大数据时简化tooltip内容
    formatter: function(params) {
      if (Array.isArray(params) && params.length > 10) {
        return `${params[0].name}: ${params.length} 个数据点`
      }
      return params.map(p => `${p.seriesName}: ${p.value}`).join('<br/>')
    }
  }
}

// 主题色彩配置
export const THEME_COLORS = {
  primary: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
  dark: ['#dd6b66', '#759aa0', '#e69d87', '#8dc1a9', '#ea7e53', '#eedd78', '#73a373', '#73b9bc', '#7289ab'],
  light: ['#37A2DA', '#32C5E9', '#67E0E3', '#9FE6B8', '#FFDB5C', '#ff9f7f', '#fb7293', '#E062AE', '#E690D1']
}

// 初始化ECharts实例的工具函数
export function initChart(container, theme = 'macarons', options = {}) {
  if (!container) {
    console.warn('ECharts container is null')
    return null
  }

  // 检查容器尺寸
  const rect = container.getBoundingClientRect()
  if (rect.width === 0 || rect.height === 0) {
    console.warn('ECharts container has no size:', rect)
    return null
  }

  try {
    // 合并性能配置
    const config = {
      ...ECHARTS_PERFORMANCE_CONFIG,
      ...options
    }

    return echarts.init(container, theme, config)
  } catch (error) {
    console.error('Failed to initialize ECharts:', error)
    return null
  }
}

// 设置图表选项的工具函数
export function setChartOption(chartInstance, option, notMerge = false, lazyUpdate = false) {
  if (!chartInstance || !option) {
    console.warn('Invalid chart instance or option')
    return
  }

  try {
    // 合并通用配置
    const mergedOption = {
      ...COMMON_CHART_OPTIONS,
      ...option
    }

    chartInstance.setOption(mergedOption, notMerge, lazyUpdate)
  } catch (error) {
    console.error('Failed to set chart option:', error)
  }
}

// 响应式图表大小调整（Vue 2 兼容）
export function setupChartResize(chartInstance, container) {
  if (!chartInstance || !container) return

  // Vue 2 环境下的resize处理
  const handleResize = () => {
    // 使用 setTimeout 来避免频繁调用
    setTimeout(() => {
      if (chartInstance && !chartInstance.isDisposed()) {
        chartInstance.resize()
      }
    }, 100)
  }

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)

  return () => {
    window.removeEventListener('resize', handleResize)
  }
}

// 销毁图表实例
export function disposeChart(chartInstance) {
  if (chartInstance && !chartInstance.isDisposed()) {
    chartInstance.dispose()
  }
}

// Vue 2 Mixin for ECharts optimization
export const EChartsOptimizationMixin = {
  data() {
    return {
      chartInstance: null,
      resizeHandler: null
    }
  },

  methods: {
    // 初始化图表
    initChart(container, theme = 'macarons', options = {}) {
      this.chartInstance = initChart(container, theme, options)

      if (this.chartInstance) {
        // 设置响应式
        this.resizeHandler = setupChartResize(this.chartInstance, container)
      }

      return this.chartInstance
    },

    // 设置图表选项
    setChartOption(option, notMerge = false, lazyUpdate = false) {
      setChartOption(this.chartInstance, option, notMerge, lazyUpdate)
    },

    // 销毁图表
    disposeChart() {
      if (this.resizeHandler) {
        this.resizeHandler()
        this.resizeHandler = null
      }

      disposeChart(this.chartInstance)
      this.chartInstance = null
    }
  },

  beforeDestroy() {
    // 组件销毁时自动清理图表
    this.disposeChart()
  }
}

// 全局ECharts优化初始化
export function initGlobalEChartsOptimization() {
  // 注册自定义主题
  echarts.registerTheme('optimized', {
    color: THEME_COLORS.primary,
    backgroundColor: 'rgba(0,0,0,0)',
    textStyle: {},
    title: {
      textStyle: {
        color: '#516b91'
      },
      subtextStyle: {
        color: '#93b7e3'
      }
    },
    line: {
      itemStyle: {
        borderWidth: 1
      },
      lineStyle: {
        width: 2
      },
      symbolSize: 4,
      symbol: 'emptyCircle',
      smooth: false
    },
    radar: {
      itemStyle: {
        borderWidth: 1
      },
      lineStyle: {
        width: 2
      },
      symbolSize: 4,
      symbol: 'emptyCircle',
      smooth: false
    },
    bar: {
      itemStyle: {
        barBorderWidth: 0,
        barBorderColor: '#ccc'
      }
    },
    pie: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    },
    scatter: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    },
    boxplot: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    },
    parallel: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    },
    sankey: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    },
    funnel: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    },
    gauge: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      }
    },
    candlestick: {
      itemStyle: {
        color: '#fd1050',
        color0: '#0cf49b',
        borderColor: '#fd1050',
        borderColor0: '#0cf49b',
        borderWidth: 1
      }
    },
    graph: {
      itemStyle: {
        borderWidth: 0,
        borderColor: '#ccc'
      },
      lineStyle: {
        width: 1,
        color: '#aaa'
      },
      symbolSize: 4,
      symbol: 'emptyCircle',
      smooth: false,
      color: THEME_COLORS.primary,
      label: {
        color: '#eee'
      }
    },
    map: {
      itemStyle: {
        areaColor: '#eee',
        borderColor: '#444',
        borderWidth: 0.5
      },
      label: {
        color: '#000'
      },
      emphasis: {
        itemStyle: {
          areaColor: 'rgba(255,215,0,0.8)',
          borderColor: '#444',
          borderWidth: 1
        },
        label: {
          color: 'rgb(100,0,0)'
        }
      }
    },
    geo: {
      itemStyle: {
        areaColor: '#eee',
        borderColor: '#444',
        borderWidth: 0.5
      },
      label: {
        color: '#000'
      },
      emphasis: {
        itemStyle: {
          areaColor: 'rgba(255,215,0,0.8)',
          borderColor: '#444',
          borderWidth: 1
        },
        label: {
          color: 'rgb(100,0,0)'
        }
      }
    },
    categoryAxis: {
      axisLine: {
        show: true,
        lineStyle: {
          color: '#6E7079'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#6E7079'
        }
      },
      axisLabel: {
        show: true,
        color: '#6E7079'
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: ['#E0E6F1']
        }
      },
      splitArea: {
        show: false,
        areaStyle: {
          color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)']
        }
      }
    },
    valueAxis: {
      axisLine: {
        show: false,
        lineStyle: {
          color: '#6E7079'
        }
      },
      axisTick: {
        show: false,
        lineStyle: {
          color: '#6E7079'
        }
      },
      axisLabel: {
        show: true,
        color: '#6E7079'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: ['#E0E6F1']
        }
      },
      splitArea: {
        show: false,
        areaStyle: {
          color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)']
        }
      }
    },
    logAxis: {
      axisLine: {
        show: false,
        lineStyle: {
          color: '#6E7079'
        }
      },
      axisTick: {
        show: false,
        lineStyle: {
          color: '#6E7079'
        }
      },
      axisLabel: {
        show: true,
        color: '#6E7079'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: ['#E0E6F1']
        }
      },
      splitArea: {
        show: false,
        areaStyle: {
          color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)']
        }
      }
    },
    timeAxis: {
      axisLine: {
        show: true,
        lineStyle: {
          color: '#6E7079'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#6E7079'
        }
      },
      axisLabel: {
        show: true,
        color: '#6E7079'
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: ['#E0E6F1']
        }
      },
      splitArea: {
        show: false,
        areaStyle: {
          color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)']
        }
      }
    },
    toolbox: {
      iconStyle: {
        borderColor: '#999'
      },
      emphasis: {
        iconStyle: {
          borderColor: '#666'
        }
      }
    },
    legend: {
      textStyle: {
        color: '#333'
      }
    },
    tooltip: {
      axisPointer: {
        lineStyle: {
          color: '#ccc',
          width: 1
        },
        crossStyle: {
          color: '#ccc',
          width: 1
        }
      }
    },
    timeline: {
      lineStyle: {
        color: '#293c55',
        width: 1
      },
      itemStyle: {
        color: '#293c55',
        borderWidth: 1
      },
      controlStyle: {
        color: '#293c55',
        borderColor: '#293c55',
        borderWidth: 0.5
      },
      checkpointStyle: {
        color: '#e43c59',
        borderColor: '#c23531'
      },
      label: {
        color: '#293c55'
      },
      emphasis: {
        itemStyle: {
          color: '#a9334c'
        },
        controlStyle: {
          color: '#293c55',
          borderColor: '#293c55',
          borderWidth: 0.5
        },
        label: {
          color: '#293c55'
        }
      }
    },
    visualMap: {
      color: ['#bf444c', '#d88273', '#f6efa6']
    },
    dataZoom: {
      backgroundColor: 'rgba(47,69,84,0)',
      dataBackgroundColor: 'rgba(47,69,84,0.3)',
      fillerColor: 'rgba(167,183,204,0.4)',
      handleColor: '#a7b7cc',
      handleSize: '100%',
      textStyle: {
        color: '#333'
      }
    },
    markPoint: {
      label: {
        color: '#eee'
      },
      emphasis: {
        label: {
          color: '#eee'
        }
      }
    }
  })

  console.log('Global ECharts optimization initialized for Vue 2')
}

// Vue 2 插件
export default {
  install(Vue) {
    // 全局混入 ECharts 优化方法
    Vue.mixin(EChartsOptimizationMixin)

    // 初始化全局优化
    initGlobalEChartsOptimization()

    // 添加全局方法
    Vue.prototype.$echarts = echarts
    Vue.prototype.$initChart = initChart
    Vue.prototype.$setChartOption = setChartOption
    Vue.prototype.$disposeChart = disposeChart
  }
}
