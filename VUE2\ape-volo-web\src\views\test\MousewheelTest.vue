<template>
  <div class="mousewheel-test">
    <el-card>
      <div slot="header">
        <span>Element UI Mousewheel 修复测试</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="checkStatus">检查修复状态</el-button>
      </div>
      <div class="test-content">
        <h3>测试说明</h3>
        <p>这个页面用于测试 Element UI mousewheel 事件的修复效果。打开浏览器开发者工具的控制台，然后滚动下面的组件，观察是否还有性能警告。</p>
        <div class="test-sections">
          <!-- 表格滚动测试 -->
          <el-card class="test-card">
            <div slot="header">表格滚动测试</div>
            <el-table
              :data="tableData"
              height="200"
              style="width: 100%"
            >
              <el-table-column prop="date" label="日期" width="180" />
              <el-table-column prop="name" label="姓名" width="180" />
              <el-table-column prop="address" label="地址" />
            </el-table>
          </el-card>
          <!-- 下拉框测试 -->
          <el-card class="test-card">
            <div slot="header">下拉框测试</div>
            <el-select v-model="selectedValue" placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-card>
          <!-- 滚动条测试 -->
          <el-card class="test-card">
            <div slot="header">滚动条测试</div>
            <el-scrollbar style="height: 200px;">
              <div style="height: 500px; padding: 20px;">
                <p v-for="i in 20" :key="i">这是第 {{ i }} 行内容，用于测试滚动性能。</p>
              </div>
            </el-scrollbar>
          </el-card>
          <!-- 日期选择器测试 -->
          <el-card class="test-card">
            <div slot="header">日期选择器测试</div>
            <el-date-picker
              v-model="selectedDate"
              type="date"
              placeholder="选择日期"
            />
          </el-card>
        </div>
        <div class="status-info">
          <h3>修复状态</h3>
          <ul>
            <li v-for="status in fixStatus" :key="status" v-html="status" />
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'MousewheelTest',
  data() {
    return {
      selectedValue: '',
      selectedDate: '',
      fixStatus: [],
      tableData: [],
      options: [
        { value: '1', label: '选项1' },
        { value: '2', label: '选项2' },
        { value: '3', label: '选项3' },
        { value: '4', label: '选项4' },
        { value: '5', label: '选项5' },
        { value: '6', label: '选项6' },
        { value: '7', label: '选项7' },
        { value: '8', label: '选项8' },
        { value: '9', label: '选项9' },
        { value: '10', label: '选项10' }
      ]
    }
  },
  mounted() {
    this.generateTableData()
    this.checkStatus()
  },
  methods: {
    generateTableData() {
      // 生成大量表格数据用于测试滚动
      for (let i = 0; i < 100; i++) {
        this.tableData.push({
          date: `2024-01-${String(i % 30 + 1).padStart(2, '0')}`,
          name: `用户${i + 1}`,
          address: `地址${i + 1} - 这是一个很长的地址用于测试表格滚动性能`
        })
      }
    },
    checkStatus() {
      // 检查修复状态
      if (this.$checkElementUIFixes) {
        this.fixStatus = this.$checkElementUIFixes()
      } else {
        this.fixStatus = ['❌ 修复检查方法不可用']
      }
    }
  }
}
</script>

<style scoped>
.mousewheel-test {
  padding: 20px;
}

.test-content {
  line-height: 1.6;
}

.test-sections {
  margin: 20px 0;
}

.test-card {
  margin-bottom: 20px;
}

.status-info {
  margin-top: 30px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.status-info ul {
  list-style: none;
  padding: 0;
}

.status-info li {
  padding: 5px 0;
  font-family: monospace;
}
</style>
