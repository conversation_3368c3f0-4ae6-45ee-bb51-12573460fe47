﻿
时间:2025-08-04 15:00:49.603
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_user`  (`username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`is_deleted`,`id`) VALUES('apevolo','系统管理员','<EMAIL>',1,1,'$2a$11$.WUDiK7CrAPKYJDSH9RtNuxarZ.u1llMs.8tWKbE76FU7G4j55E62','163519427764317','13699797575','/uploads/file/avatar/20231010143458_1711631391147429888.png',NULL,'男','0','apevolo','2025-08-04 15:00:49.459',0,'***************'),  ('guest','游客','<EMAIL>',0,1,'$2a$11$9BhSJpUSBlfjhemHVho0vej7rOH7PVhu5bqbhRcmwJMEyyVqGLTJe','163519427764317','13699797575','/uploads/file/avatar/20231010143458_1711631391147429888.png',NULL,'男','1001','apevolo','2025-08-04 15:00:49.469',0,'***************') ;SELECT LAST_INSERT_ROWID();

[耗时]:116.058ms,请检查并进行优化！


时间:2025-08-04 15:00:49.760
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_role`  (`name`,`level`,`description`,`data_scope_type`,`permission`,`create_by`,`create_time`,`is_deleted`,`id`) VALUES('内置管理员','1','内置管理员','1','admin','apevolo','2025-08-04 15:00:49.621',0,'***************'),  ('游客','99','游客','1','guest','apevolo','2025-08-04 15:00:49.626',0,'***************') ;SELECT LAST_INSERT_ROWID();

[耗时]:130.2089ms,请检查并进行优化！


时间:2025-08-04 15:00:50.277
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_menu`  (`title`,`path`,`permission`,`i_frame`,`component`,`component_name`,`parent_id`,`sort`,`icon`,`type`,`cache`,`hidden`,`sub_count`,`create_by`,`create_time`,`is_deleted`,`id`) VALUES('权限管理','permission',NULL,0,NULL,NULL,'0','1','permission','1',0,0,'6','apevolo','2025-08-04 15:00:49.776',0,'163536122466373'),  ('用户管理','user','user_list',0,'permission/user/index','User','163536122466373','1','peoples','2',0,0,'3','apevolo','2025-08-04 15:00:49.780',0,'163536122466374'),  ('用户新增',NULL,'user_add',0,'','','163536122466374','1','','3',0,0,'0','apevolo','2025-08-04 15:00:49.785',0,'163536122466375'),  ('用户编辑',NULL,'user_edit',0,'','','163536122466374','2','','3',0,0,'0','apevolo','2025-08-04 15:00:49.789',0,'163536122466376'),  ('用户删除',NULL,'user_del',0,'','','163536122466374','3','','3',0,0,'0','apevolo','2025-08-04 15:00:49.792',0,'163536122466377'),  ('用户导出','','user_down',0,'','','163536122466374','4','','3',0,0,'0','apevolo','2025-08-04 15:00:49.794',0,'163536122466378'),  ('角色管理','role','role_list',0,'permission/role/index','Role','163536122466373','2','role','2',0,0,'3','apevolo','2025-08-04 15:00:49.797',0,'163536122466379'),  ('角色创建',NULL,'role_add',0,'','','163536122466379','1','','3',0,0,'0','apevolo','2025-08-04 15:00:49.800',0,'163536122466380'),  ('角色修改',NULL,'role_edit',0,'','','163536122466379','2','','3',0,0,'0','apevolo','2025-08-04 15:00:49.802',0,'163536122466381'),  ('角色删除',NULL,'role_del',0,'','','163536122466379','3','','3',0,0,'0','apevolo','2025-08-04 15:00:49.805',0,'163536122466382'),  ('角色导出','','role_down',0,'','','163536122466379','4','','3',0,0,'0','apevolo','2025-08-04 15:00:49.809',0,'163536122466383'),  ('菜单管理','menu','menu_list',0,'permission/menu/index','Menu','163536122466373','3','menu','2',0,0,'3','apevolo','2025-08-04 15:00:49.813',0,'163536122466384'),  ('菜单新增',NULL,'menu_add',0,'','','163536122466384','1','','3',0,0,'0','apevolo','2025-08-04 15:00:49.818',0,'163536122466385'),  ('菜单编辑',NULL,'menu_edit',0,'','','163536122466384','2','','3',0,0,'0','apevolo','2025-08-04 15:00:49.821',0,'163536122466386'),  ('菜单删除',NULL,'menu_del',0,'','','163536122466384','3','','3',0,0,'0','apevolo','2025-08-04 15:00:49.825',0,'163536122466387'),  ('菜单导出','','menu_down',0,'','','163536122466384','4','','3',0,0,'0','apevolo','2025-08-04 15:00:49.828',0,'163536122466388'),  ('部门管理','dept','dept_list',0,'permission/dept/index','Dept','163536122466373','4','dept','2',0,0,'3','apevolo','2025-08-04 15:00:49.831',0,'163536122466389'),  ('部门新增',NULL,'dept_add',0,'','','163536122466389','1','','3',0,0,'0','apevolo','2025-08-04 15:00:49.834',0,'163536122466390'),  ('部门编辑',NULL,'dept_edit',0,'','','163536122466389','2','','3',0,0,'0','apevolo','2025-08-04 15:00:49.837',0,'163536122466391'),  ('部门删除',NULL,'dept_del',0,'','','163536122466389','3','','3',0,0,'0','apevolo','2025-08-04 15:00:49.842',0,'163536122466392'),  ('部门导出','','dept_down',0,'','','163536122466389','4','','3',0,0,'0','apevolo','2025-08-04 15:00:49.845',0,'163536122466393'),  ('岗位管理','job','job_list',0,'permission/job/index','Job','163536122466373','5','Steve-Jobs','2',0,0,'3','apevolo','2025-08-04 15:00:49.848',0,'163536122466394'),  ('岗位新增',NULL,'job_add',0,'','','163536122466394','1','','3',0,0,'0','apevolo','2025-08-04 15:00:49.851',0,'163536122466395'),  ('岗位编辑',NULL,'job_edit',0,'','','163536122466394','2','','3',0,0,'0','apevolo','2025-08-04 15:00:49.854',0,'163536122466396'),  ('岗位删除',NULL,'job_del',0,'','','163536122466394','3','','3',0,0,'0','apevolo','2025-08-04 15:00:49.856',0,'163536122466397'),  ('岗位导出','','job_down',0,'','','163536122466394','4','','3',0,0,'0','apevolo','2025-08-04 15:00:49.859',0,'163536122466398'),  ('系统管理','system',NULL,0,NULL,NULL,'0','2','system','1',0,0,'6','apevolo','2025-08-04 15:00:49.862',0,'163536122466399'),  ('字典管理','dict','dict_list',0,'system/dict/index','Dict','163536122466399','1','dictionary','2',0,0,'3','apevolo','2025-08-04 15:00:49.866',0,'163536122466401'),  ('字典新增',NULL,'dict_add',0,'','','163536122466401','1','','3',0,0,'0','apevolo','2025-08-04 15:00:49.870',0,'163536122466402'),  ('字典编辑',NULL,'dict_edit',0,'','','163536122466401','2','','3',0,0,'0','apevolo','2025-08-04 15:00:49.872',0,'163536122466403'),  ('字典删除',NULL,'dict_del',0,'','','163536122466401','3','','3',0,0,'0','apevolo','2025-08-04 15:00:49.876',0,'163536122466404'),  ('字典导出','','dict_down',0,'','','163536122466401','4','','3',0,0,'0','apevolo','2025-08-04 15:00:49.879',0,'163536122466405'),  ('作业调度','tasks','timing_list',0,'system/tasks/index','Timing','163536122466399','2','timing','2',0,0,'4','apevolo','2025-08-04 15:00:49.882',0,'163536122466406'),  ('作业新增',NULL,'timing_add',0,'','','163536122466406','1','','3',0,0,'0','apevolo','2025-08-04 15:00:49.885',0,'163536122466407'),  ('作业编辑',NULL,'timing_edit',0,'','','163536122466406','2','','3',0,0,'0','apevolo','2025-08-04 15:00:49.888',0,'163536122466408'),  ('作业删除',NULL,'timing_del',0,'','','163536122466406','3','','3',0,0,'0','apevolo','2025-08-04 15:00:49.892',0,'163536122466409'),  ('作业日志',NULL,'taskslog_list',0,'','','163536122466406','4','','3',0,0,'0','apevolo','2025-08-04 15:00:49.895',0,'163536122466410'),  ('作业导出','','timing_down',0,'','','163536122466406','5','','3',0,0,'0','apevolo','2025-08-04 15:00:49.898',0,'163536122466411'),  ('全局设置','setting','setting_list',0,'system/setting/index','Setting','163536122466399','3','setting','2',0,0,'4','apevolo','2025-08-04 15:00:49.900',0,'163536122466412'),  ('设置新增',NULL,'setting_add',0,'','','163536122466412','1','','3',0,0,'0','apevolo','2025-08-04 15:00:49.903',0,'163536122466413'),  ('设置编辑',NULL,'setting_edit',0,'','','163536122466412','2','','3',0,0,'0','apevolo','2025-08-04 15:00:49.905',0,'163536122466414'),  ('设置删除',NULL,'setting_del',0,'','','163536122466412','3','','3',0,0,'0','apevolo','2025-08-04 15:00:49.908',0,'163536122466415'),  ('设置导出','','setting_down',0,'','','163536122466412','4','','3',0,0,'0','apevolo','2025-08-04 15:00:49.911',0,'163536122466416'),  ('应用密钥','appsecret','appSecret_list',0,'system/appsecret/index','AppSecret','163536122466399','4','appSecret','2',0,0,'4','apevolo','2025-08-04 15:00:49.914',0,'163536122466417'),  ('密钥新增',NULL,'appSecret_add',0,NULL,NULL,'163536122466417','1',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.917',0,'163536122466418'),  ('密钥编辑',NULL,'appSecret_edit',0,NULL,NULL,'163536122466417','2',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.920',0,'163536122466419'),  ('密钥删除',NULL,'appSecret_del',0,NULL,NULL,'163536122466417','3',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.922',0,'163536122466420'),  ('密钥导出',NULL,'appSecret_down',0,NULL,NULL,'163536122466417','4',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.925',0,'163536122466421'),  ('系统监控','monitor',NULL,0,NULL,NULL,'0','3','monitor','1',0,0,'4','apevolo','2025-08-04 15:00:49.929',0,'***************'),  ('审计日志','auditing','auditing_list',0,'monitor/auditing/index','Audit','***************','1','log','2',0,0,'0','apevolo','2025-08-04 15:00:49.931',0,'163536122466423'),  ('异常日志','log','log_list',0,'monitor/log/index','Log','***************','2','error','2',0,0,'0','apevolo','2025-08-04 15:00:49.934',0,'163536122466424'),  ('在线用户','online','online_list',0,'monitor/online/index','OnlineUser','***************','3','Steve-Jobs','2',0,0,'0','apevolo','2025-08-04 15:00:49.936',0,'***************'),  ('服务监控','server','server_list',0,'monitor/server/index','ServerMonitor','***************','4','codeConsole','2',0,0,'0','apevolo','2025-08-04 15:00:49.939',0,'***************'),  ('消息服务','message',NULL,0,NULL,NULL,'0','4','message','1',0,0,'1','apevolo','2025-08-04 15:00:49.942',0,'***************'),  ('邮箱账户','email/account','emailAccount_list',0,'message/email/account/index','Email','***************','1','email','2',0,0,'3','apevolo','2025-08-04 15:00:49.945',0,'***************'),  ('邮箱账户新增',NULL,'emailAccount_add',0,NULL,NULL,'***************','1',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.948',0,'***************'),  ('邮箱账户编辑',NULL,'emailAccount_edit',0,NULL,NULL,'***************','2',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.950',0,'***************'),  ('邮箱账户删除',NULL,'emailAccount_del',0,NULL,NULL,'***************','3',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.952',0,'***************'),  ('邮箱账户导出','','emailAccount_down',0,'','','***************','4','','3',0,0,'0','apevolo','2025-08-04 15:00:49.955',0,'***************'),  ('邮件模板','email/template','emailTemplate_list',0,'message/email/template/index','Template','***************','2','email','2',0,0,'3','apevolo','2025-08-04 15:00:49.957',0,'***************'),  ('邮件模板新增',NULL,'emailTemplate_add',0,NULL,NULL,'***************','1',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.958',0,'***************'),  ('邮件模板编辑',NULL,'emailTemplate_edit',0,NULL,NULL,'***************','2',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.961',0,'***************'),  ('邮件模板删除',NULL,'emailTemplate_del',0,NULL,NULL,'***************','3',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.962',0,'***************'),  ('邮件队列','email/queued','queuedEmail_list',0,'queued/index','Queue','***************','3','list','2',0,0,'3','apevolo','2025-08-04 15:00:49.963',0,'***************'),  ('邮件队列添加',NULL,'queuedEmail_add',0,NULL,NULL,'***************','1',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.965',0,'163536122466438'),  ('邮件队列编辑',NULL,'queuedEmail_edit',0,NULL,NULL,'***************','2',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.966',0,'163536122466439'),  ('邮件队列删除',NULL,'queuedEmail_del',0,NULL,NULL,'***************','3',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.968',0,'163536122466440'),  ('组件管理','components',NULL,0,NULL,NULL,'0','5','zujian','1',0,0,'4','apevolo','2025-08-04 15:00:49.969',0,'163536122466441'),  ('图标库','Icon',NULL,0,'components/icons/index','icons','163536122466441','1','icon','2',0,0,'0','apevolo','2025-08-04 15:00:49.972',0,'163536122466442'),  ('图表库','echarts','',0,'components/Echarts','Echarts','163536122466441','2','chart','2',0,0,'0','apevolo','2025-08-04 15:00:49.973',0,'163536122466443'),  ('文件存储','storage','storage_list',0,'system/storage/index',NULL,'163536122466399','5','file','2',0,0,'4','apevolo','2025-08-04 15:00:49.975',0,'163536122466444'),  ('文件新增',NULL,'storage_add',0,NULL,NULL,'163536122466444','1',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.977',0,'163536122466445'),  ('文件编辑',NULL,'storage_edit',0,NULL,NULL,'163536122466444','2',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.978',0,'163536122466446'),  ('文件删除',NULL,'storage_del',0,NULL,NULL,'163536122466444','3',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.980',0,'163536122466447'),  ('文件导出',NULL,'storage_down',0,NULL,NULL,'163536122466444','4',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.982',0,'163536122466448'),  ('Api管理','interface','apis_list',0,'permission/interface/index',NULL,'163536122466373','6','api','2',0,0,'3','apevolo','2025-08-04 15:00:49.983',0,'163536122466449'),  ('Api新增',NULL,'apis_add',0,NULL,NULL,'163536122466449','1',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.984',0,'163536122466450'),  ('Api编辑',NULL,'apis_edit',0,NULL,NULL,'163536122466449','2',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.985',0,'163536122466451'),  ('Api删除',NULL,'apis_del',0,NULL,NULL,'163536122466449','3',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.988',0,'163536122466452'),  ('租户管理','tenant','tenant_list',0,'system/tenant/index',NULL,'163536122466399','6','tenant','2',0,0,'4','apevolo','2025-08-04 15:00:49.990',0,'163536122466453'),  ('租户新增',NULL,'tenant_add',0,NULL,NULL,'163536122466453','1',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.992',0,'163536122466454'),  ('租户编辑',NULL,'tenant_edit',0,NULL,NULL,'163536122466453','2',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.993',0,'163536122466455'),  ('租户删除',NULL,'tenant_del',0,NULL,NULL,'163536122466453','3',NULL,'3',0,0,'0','apevolo','2025-08-04 15:00:49.994',0,'163536122466456'),  ('租户导出','','tenant_down',0,'','','163536122466453','4','','3',0,0,'0','apevolo','2025-08-04 15:00:49.996',0,'163536122466457') ;SELECT LAST_INSERT_ROWID();

[耗时]:276.7709ms,请检查并进行优化！


时间:2025-08-04 15:00:50.427
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_department`  (`name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`is_deleted`,`id`) VALUES('集团总部','0','1',1,'4','apevolo','2025-08-04 15:00:50.287',0,'163519427764312'),  ('信息部','163519427764312','2',1,'2','apevolo','2025-08-04 15:00:50.289',0,'163519427764313'),  ('财务部','163519427764312','3',1,'2','apevolo','2025-08-04 15:00:50.291',0,'163519427764314'),  ('销售部','163519427764312','4',1,'2','apevolo','2025-08-04 15:00:50.292',0,'163519427764315'),  ('市场部','163519427764312','5',1,'2','apevolo','2025-08-04 15:00:50.292',0,'163519427764316'),  ('研发部','163519427764313','6',1,'0','apevolo','2025-08-04 15:00:50.293',0,'163519427764317'),  ('产品部','163519427764313','7',1,'0','apevolo','2025-08-04 15:00:50.293',0,'163519427764318'),  ('财务会计部','163519427764314','8',1,'0','apevolo','2025-08-04 15:00:50.294',0,'163519427764319'),  ('审计风控部','163519427764314','9',1,'0','apevolo','2025-08-04 15:00:50.295',0,'163519427764320'),  ('客服部','163519427764315','10',1,'0','apevolo','2025-08-04 15:00:50.295',0,'163519427764321'),  ('售后部','163519427764315','11',1,'0','apevolo','2025-08-04 15:00:50.296',0,'163519427764322'),  ('营销部','163519427764316','12',1,'0','apevolo','2025-08-04 15:00:50.296',0,'163519427764323'),  ('策划部','163519427764316','13',1,'0','apevolo','2025-08-04 15:00:50.297',0,'163519427764324') ;SELECT LAST_INSERT_ROWID();

[耗时]:128.2465ms,请检查并进行优化！


时间:2025-08-04 15:00:50.563
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_job`  (`name`,`sort`,`enabled`,`create_by`,`create_time`,`is_deleted`,`id`) VALUES('全栈开发','1',1,'apevolo','2025-08-04 15:00:50.435',0,'***************'),  ('软件测试','2',1,'apevolo','2025-08-04 15:00:50.436',0,'***************') ;SELECT LAST_INSERT_ROWID();

[耗时]:124.9508ms,请检查并进行优化！


时间:2025-08-04 15:00:50.804
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_setting`  (`name`,`value`,`enabled`,`description`,`create_by`,`create_time`,`is_deleted`,`id`) VALUES('DefaultEmailAccountId','***************',1,'默认系统邮件发送账户','apevolo','2025-08-04 15:00:50.578',0,'***************'),  ('IsAdminNotAuthentication','false',1,'系统管理免接口鉴权','apevolo','2025-08-04 15:00:50.581',0,'***************'),  ('IsExceptionLogSaveDB','true',1,'异常日志是否保存到数据库','apevolo','2025-08-04 15:00:50.582',0,'***************'),  ('IsAuditLogSaveDB','true',1,'审计日志是否保存到数据库','apevolo','2025-08-04 15:00:50.582',0,'***************') ;SELECT LAST_INSERT_ROWID();

[耗时]:220.4548ms,请检查并进行优化！


时间:2025-08-04 15:00:51.016
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_dict`  (`dict_type`,`name`,`description`,`create_by`,`create_time`,`is_deleted`,`id`) VALUES('1','dict_type','字典类型(系统or业务)','apevolo','2025-08-04 15:00:50.813',0,'***************'),  ('2','job_status','岗位状态','apevolo','2025-08-04 15:00:50.815',0,'163519427764328'),  ('2','dept_status','部门状态','apevolo','2025-08-04 15:00:50.816',0,'163519427764331'),  ('2','user_status','用户状态','apevolo','2025-08-04 15:00:50.817',0,'163519427764334'),  ('2','email_message_template_status','邮件消息模板状态','apevolo','2025-08-04 15:00:50.817',0,'163519427764337'),  ('2','setting_status','全局设置状态','apevolo','2025-08-04 15:00:50.818',0,'163519427764340'),  ('1','task_trigger_type','作业触发器类型','apevolo','2025-08-04 15:00:50.818',0,'163519427764343'),  ('1','tenant_type','租户类型','apevolo','2025-08-04 15:00:50.818',0,'163519427764346'),  ('1','db_type','数据库类型','apevolo','2025-08-04 15:00:50.819',0,'163519427764349'),  ('2','data_scope_type','数据权限','apevolo','2025-08-04 15:00:50.819',0,'163519427768391') ;SELECT LAST_INSERT_ROWID();

[耗时]:195.9317ms,请检查并进行优化！


时间:2025-08-04 15:00:51.203
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_dict_detail`  (`dict_id`,`label`,`value`,`dict_sort`,`create_by`,`create_time`,`is_deleted`,`id`) VALUES('***************','系统类','1','2','apevolo','2025-08-04 15:00:51.029',0,'163519427764326'),  ('***************','业务类','2','1','apevolo','2025-08-04 15:00:51.031',0,'163519427764327'),  ('163519427764328','停用','false','2','apevolo','2025-08-04 15:00:51.032',0,'163519427764329'),  ('163519427764328','启用','true','1','apevolo','2025-08-04 15:00:51.032',0,'163519427764330'),  ('163519427764331','停用','false','2','apevolo','2025-08-04 15:00:51.033',0,'163519427764332'),  ('163519427764331','启用','true','1','apevolo','2025-08-04 15:00:51.033',0,'163519427764333'),  ('163519427764334','禁用','false','2','apevolo','2025-08-04 15:00:51.034',0,'163519427764335'),  ('163519427764334','激活','true','1','apevolo','2025-08-04 15:00:51.034',0,'163519427764336'),  ('163519427764337','启用','true','1','apevolo','2021-01-01 00:00:00.000',0,'163519427764338'),  ('163519427764337','禁用','false','2','apevolo','2025-08-04 15:00:51.035',0,'163519427764339'),  ('163519427764340','启用','true','1','apevolo','2021-01-01 00:00:00.000',0,'163519427764341'),  ('163519427764340','禁用','false','2','apevolo','2025-08-04 15:00:51.036',0,'163519427764342'),  ('163519427764343','cron','1','1','apevolo','2021-01-01 00:00:00.000',0,'163519427764344'),  ('163519427764343','simple','0','2','apevolo','2025-08-04 15:00:51.037',0,'163519427764345'),  ('163519427764346','ID隔离','1','1','apevolo','2021-01-01 00:00:00.000',0,'163519427764347'),  ('163519427764346','库隔离','2','2','apevolo','2025-08-04 15:00:51.038',0,'163519427764348'),  ('163519427764349','MySql','0','1','apevolo','2021-01-01 00:00:00.000',0,'163519427764350'),  ('163519427764349','SqlServer','1','2','apevolo','2025-08-04 15:00:51.040',0,'163519427764351'),  ('163519427764349','Sqlite','2','3','apevolo','2021-01-01 00:00:00.000',0,'163519427768389'),  ('163519427764349','Oracle','3','4','apevolo','2025-08-04 15:00:51.041',0,'163519427768390'),  ('163519427768391','全部','1','1','apevolo','2025-08-04 15:00:51.042',0,'163519427768392'),  ('163519427768391','本人','2','2','apevolo','2025-08-04 15:00:51.042',0,'163519427768393'),  ('163519427768391','本部门','3','3','apevolo','2025-08-04 15:00:51.043',0,'163519427768394'),  ('163519427768391','本部门及以下','4','4','apevolo','2025-08-04 15:00:51.043',0,'163519427768395'),  ('163519427768391','自定义','5','5','apevolo','2025-08-04 15:00:51.044',0,'163519427768396') ;SELECT LAST_INSERT_ROWID();

[耗时]:157.2511ms,请检查并进行优化！


时间:2025-08-04 15:00:51.425
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_quartz_job`  (`task_name`,`task_group`,`cron`,`assembly_name`,`class_name`,`description`,`principal`,`alert_email`,`pause_after_failure`,`run_times`,`start_time`,`end_time`,`trigger_type`,`interval_second`,`cycle_run_times`,`is_enable`,`run_params`,`create_by`,`create_time`,`is_deleted`,`id`) VALUES('Console_Write','TestConsole','*/5 * * * * ?','Ape.Volo.TaskService','TestConsoleWriteJobService','Test','apevolo',NULL,0,'0',NULL,NULL,'1','0','0',0,NULL,'apevolo','2025-08-04 15:00:51.220',0,'***************'),  ('Email_Send','邮件作业','*/5 * * * * ?','Ape.Volo.TaskService','SendEmailJobService','执行邮件发送','apevolo',NULL,0,'0',NULL,NULL,'1','0','0',0,NULL,'apevolo','2025-08-04 15:00:51.226',0,'***************') ;SELECT LAST_INSERT_ROWID();

[耗时]:195.8278ms,请检查并进行优化！


时间:2025-08-04 15:00:51.712
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `email_account`  
           (`email`,`display_name`,`host`,`port`,`username`,`password`,`enable_ssl`,`use_default_credentials`,`create_by`,`create_time`,`is_deleted`,`id`)
     VALUES
           (@email,@display_name,@host,@port,@username,@password,@enable_ssl,@use_default_credentials,@create_by,@create_time,@is_deleted,@id) ; 
[Pars]:
[Name]:@email [Value]:your email [Type]:String    
[Name]:@display_name [Value]:your dispalyName [Type]:String    
[Name]:@host [Value]:smtp.qq.com [Type]:String    
[Name]:@port [Value]:465 [Type]:Int32    
[Name]:@username [Value]:your name [Type]:String    
[Name]:@password [Value]:your pass [Type]:String    
[Name]:@enable_ssl [Value]:True [Type]:Boolean    
[Name]:@use_default_credentials [Value]:False [Type]:Boolean    
[Name]:@create_by [Value]:apevolo [Type]:String    
[Name]:@create_time [Value]:2025-08-04 15:00:51 [Type]:DateTime    
[Name]:@is_deleted [Value]:False [Type]:Boolean    
[Name]:@id [Value]:*************** [Type]:Int64    
[耗时]:266.4535ms,请检查并进行优化！


时间:2025-08-04 15:00:51.873
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `email_message_template`  
           (`name`,`bcc_email_addresses`,`subject`,`body`,`is_active`,`email_account_id`,`create_by`,`create_time`,`is_deleted`,`id`)
     VALUES
           (@name,@bcc_email_addresses,@subject,@body,@is_active,@email_account_id,@create_by,@create_time,@is_deleted,@id) ; 
[Pars]:
[Name]:@name [Value]:EmailVerificationCode [Type]:String    
[Name]:@bcc_email_addresses [Value]: [Type]:String    
[Name]:@subject [Value]:申请邮箱验证码 [Type]:String    
[Name]:@body [Value]:<head>
    <base target="_blank" />
    <style type="text/css">::-webkit-scrollbar{ display: none; }</style>
    <style id="cloudAttachStyle" type="text/css">#divNeteaseBigAttach, #divNeteaseBigAttach_bak{display:none;}</style>
    <style id="blockquoteStyle" type="text/css">blockquote{display:none;}</style>
    <style type="text/css">
        body{font-size:14px;font-family:arial,verdana,sans-serif;line-height:1.666;padding:0;margin:0;overflow:auto;white-space:normal;word-wrap:break-word;min-height:100px}
        td, input, button, select, body{font-family:Helvetica, 'Microsoft Yahei', verdana}
        pre {white-space:pre-wrap;white-space:-moz-pre-wrap;white-space:-pre-wrap;white-space:-o-pre-wrap;word-wrap:break-word;width:95%}
        th,td{font-family:arial,verdana,sans-serif;line-height:1.666}
        img{ border:0}
        header,footer,section,aside,article,nav,hgroup,figure,figcaption{display:block}
        blockquote{margin-right:0px}
    </style>
</head>
<body tabindex="0" role="listitem">
<table width="700" border="0" align="center" cellspacing="0" style="width:700px;">
    <tbody>
    <tr>
        <td>
            <div style="width:700px;margin:0 auto;border-bottom:1px solid #ccc;margin-bottom:30px;">
                <table border="0" cellpadding="0" cellspacing="0" width="700" height="39" style="font:12px Tahoma, Arial, 宋体;">
                    <tbody><tr><td width="210"></td></tr></tbody>
                </table>
            </div>
            <div style="width:680px;padding:0 10px;margin:0 auto;">
                <div style="line-height:1.5;font-size:14px;margin-bottom:25px;color:#4d4d4d;">
                    <strong style="display:block;margin-bottom:15px;">尊敬的用户：<span style="color:#f60;font-size: 16px;"></span>您好！</strong>
                    <strong style="display:block;margin-bottom:15px;">
                        您正在进行<span style="color: red">更改邮箱</span>操作，请在验证码输入框中输入：<span style="color:#f60;font-size: 24px">%captcha%</span>，以完成操作，五分钟内有效。
                    </strong>
                </div>
                <div style="margin-bottom:30px;">
                    <small style="display:block;margin-bottom:20px;font-size:12px;">
                        <p style="color:#747474;">
                            注意：此操作可能会修改您的密码、登录邮箱或绑定手机。如非本人操作，请及时登录并修改密码以保证帐户安全
                            <br>（工作人员不会向你索取此验证码，请勿泄漏！)
                        </p>
                    </small>
                </div>
            </div>
            <div style="width:700px;margin:0 auto;">
                <div style="padding:10px 10px 0;border-top:1px solid #ccc;color:#747474;margin-bottom:20px;line-height:1.3em;font-size:12px;">
                    <p>此为系统邮件，请勿回复<br>
                        请保管好您的邮箱，避免账号被他人盗用
                    </p>
                    <p>ApeVolo Administrator</p>
                </div>
            </div>
        </td>
    </tr>
    </tbody>
</table>
</body> [Type]:String    
[Name]:@is_active [Value]:True [Type]:Boolean    
[Name]:@email_account_id [Value]:*************** [Type]:Int64    
[Name]:@create_by [Value]:apevolo [Type]:String    
[Name]:@create_time [Value]:2025-08-04 15:00:51 [Type]:DateTime    
[Name]:@is_deleted [Value]:False [Type]:Boolean    
[Name]:@id [Value]:*************** [Type]:Int64    
[耗时]:145.1813ms,请检查并进行优化！


时间:2025-08-04 15:00:52.076
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_user_role`  (`user_id`,`role_id`) VALUES('***************','***************'),  ('***************','***************') ;SELECT LAST_INSERT_ROWID();

[耗时]:187.6072ms,请检查并进行优化！


时间:2025-08-04 15:00:52.270
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_user_job`  (`user_id`,`job_id`) VALUES('***************','***************'),  ('***************','***************'),  ('***************','***************') ;SELECT LAST_INSERT_ROWID();

[耗时]:174.4589ms,请检查并进行优化！


时间:2025-08-04 15:00:52.420
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_role_menu`  (`role_id`,`menu_id`) VALUES('***************','163536122466399'),  ('***************','163536122466374'),  ('***************','163536122466379'),  ('***************','163536122466384'),  ('***************','***************'),  ('***************','163536122466423'),  ('***************','***************'),  ('***************','163536122466442'),  ('***************','163536122466424'),  ('***************','163536122466389'),  ('***************','163536122466394'),  ('***************','163536122466401'),  ('***************','***************'),  ('***************','163536122466375'),  ('***************','163536122466376'),  ('***************','163536122466377'),  ('***************','163536122466380'),  ('***************','163536122466381'),  ('***************','163536122466382'),  ('***************','163536122466385'),  ('***************','163536122466386'),  ('***************','163536122466387'),  ('***************','163536122466390'),  ('***************','163536122466391'),  ('***************','163536122466392'),  ('***************','163536122466395'),  ('***************','163536122466396'),  ('***************','163536122466397'),  ('***************','163536122466402'),  ('***************','163536122466403'),  ('***************','163536122466404'),  ('***************','163536122466407'),  ('***************','163536122466408'),  ('***************','163536122466409'),  ('***************','***************'),  ('***************','163536122466443'),  ('***************','163536122466406'),  ('***************','163536122466410'),  ('***************','163536122466441'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','163536122466438'),  ('***************','163536122466439'),  ('***************','163536122466440'),  ('***************','163536122466373'),  ('***************','163536122466378'),  ('***************','163536122466383'),  ('***************','163536122466388'),  ('***************','163536122466393'),  ('***************','163536122466398'),  ('***************','163536122466405'),  ('***************','163536122466411'),  ('***************','***************'),  ('***************','163536122466412'),  ('***************','163536122466413'),  ('***************','163536122466414'),  ('***************','163536122466415'),  ('***************','163536122466416'),  ('***************','163536122466417'),  ('***************','163536122466418'),  ('***************','163536122466419'),  ('***************','163536122466420'),  ('***************','163536122466421'),  ('***************','163536122466444'),  ('***************','163536122466445'),  ('***************','163536122466446'),  ('***************','163536122466447'),  ('***************','163536122466448'),  ('***************','163536122466449'),  ('***************','163536122466450'),  ('***************','163536122466451'),  ('***************','163536122466452'),  ('***************','163536122466453'),  ('***************','163536122466454'),  ('***************','163536122466455'),  ('***************','163536122466456'),  ('***************','163536122466457'),  ('***************','163536122466373'),  ('***************','163536122466374'),  ('***************','163536122466389'),  ('***************','163536122466394') ;SELECT LAST_INSERT_ROWID();

[耗时]:124.3318ms,请检查并进行优化！


时间:2025-08-04 15:00:52.676
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_apis`  (`group`,`url`,`description`,`method`,`create_by`,`create_time`,`is_deleted`,`id`) VALUES('授权管理','/auth/login','用户登录','POST','apevolo','2025-08-04 15:00:52.453',0,'163529871138884'),  ('授权管理','/auth/refreshtoken','刷新Token(必需)','POST','apevolo','2025-08-04 15:00:52.454',0,'163529871138885'),  ('授权管理','/auth/info','个人信息(必需)','GET','apevolo','2025-08-04 15:00:52.455',0,'163529871138886'),  ('授权管理','/auth/captcha','获取验证码','GET','apevolo','2025-08-04 15:00:52.455',0,'163529871138887'),  ('授权管理','/auth/code/reset/email','获取邮箱验证码','POST','apevolo','2025-08-04 15:00:52.459',0,'163529871138888'),  ('授权管理','/auth/logout','用户登出','DELETE','apevolo','2025-08-04 15:00:52.459',0,'163529871138889'),  ('用户管理','/api/user/create','创建','POST','apevolo','2025-08-04 15:00:52.461',0,'163529871138890'),  ('用户管理','/api/user/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.461',0,'163529871138891'),  ('用户管理','/api/user/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.462',0,'163529871138892'),  ('用户管理','/api/user/update/center','更新个人信息','PUT','apevolo','2025-08-04 15:00:52.462',0,'163529871138893'),  ('用户管理','/api/user/update/password','更新密码','POST','apevolo','2025-08-04 15:00:52.462',0,'163529871138894'),  ('用户管理','/api/user/update/email','更新邮箱','POST','apevolo','2025-08-04 15:00:52.463',0,'163529871138895'),  ('用户管理','/api/user/update/avatar','更新头像','POST','apevolo','2025-08-04 15:00:52.463',0,'163529871138896'),  ('用户管理','/api/user/query','查询','GET','apevolo','2025-08-04 15:00:52.464',0,'163529871138897'),  ('用户管理','/api/user/download','导出','GET','apevolo','2025-08-04 15:00:52.464',0,'163529871138898'),  ('角色管理','/api/role/create','创建','POST','apevolo','2025-08-04 15:00:52.464',0,'163529871138899'),  ('角色管理','/api/role/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.465',0,'163529871138900'),  ('角色管理','/api/role/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.465',0,'163529871138901'),  ('角色管理','/api/role/querySingle','查看指定角色','GET','apevolo','2025-08-04 15:00:52.466',0,'163529871138902'),  ('角色管理','/api/role/query','查询','GET','apevolo','2025-08-04 15:00:52.466',0,'163529871138903'),  ('角色管理','/api/role/download','导出','GET','apevolo','2025-08-04 15:00:52.466',0,'163529871138904'),  ('角色管理','/api/role/queryAll','查询全部','GET','apevolo','2025-08-04 15:00:52.467',0,'163529871138905'),  ('角色管理','/api/role/level','当前用户等级','GET','apevolo','2025-08-04 15:00:52.467',0,'163529871138906'),  ('部门管理','/api/dept/create','创建','POST','apevolo','2025-08-04 15:00:52.468',0,'163529871138907'),  ('部门管理','/api/dept/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.468',0,'163529871138908'),  ('部门管理','/api/dept/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.469',0,'163529871138909'),  ('部门管理','/api/dept/query','查询','GET','apevolo','2025-08-04 15:00:52.469',0,'163529871138910'),  ('部门管理','/api/dept/download','导出','GET','apevolo','2025-08-04 15:00:52.469',0,'163529871138911'),  ('部门管理','/api/dept/superior','获取同级、父级部门','GET','apevolo','2025-08-04 15:00:52.470',0,'163529871138912'),  ('菜单管理','/api/menu/create','创建','POST','apevolo','2025-08-04 15:00:52.471',0,'163529871138913'),  ('菜单管理','/api/menu/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.471',0,'163529871138914'),  ('菜单管理','/api/menu/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.472',0,'163529871138915'),  ('菜单管理','/api/menu/build','构建菜单(必需)','GET','apevolo','2025-08-04 15:00:52.472',0,'163529871138916'),  ('菜单管理','/api/menu/lazy','子菜单','GET','apevolo','2025-08-04 15:00:52.472',0,'163529871138917'),  ('菜单管理','/api/menu/query','查询','GET','apevolo','2025-08-04 15:00:52.473',0,'163529871138918'),  ('菜单管理','/api/menu/download','导出','GET','apevolo','2025-08-04 15:00:52.473',0,'163529871138919'),  ('菜单管理','/api/menu/superior','获取同级、父级菜单','GET','apevolo','2025-08-04 15:00:52.475',0,'163529871138920'),  ('菜单管理','/api/menu/child','获取所有子级菜单Id','GET','apevolo','2025-08-04 15:00:52.476',0,'163529871138921'),  ('岗位管理','/api/job/create','创建','POST','apevolo','2025-08-04 15:00:52.476',0,'163529871138922'),  ('岗位管理','/api/job/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.477',0,'163529871138923'),  ('岗位管理','/api/job/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.477',0,'163529871138924'),  ('岗位管理','/api/job/query','查询','GET','apevolo','2025-08-04 15:00:52.477',0,'163529871138925'),  ('岗位管理','/api/job/queryAll','查询全部','GET','apevolo','2025-08-04 15:00:52.478',0,'163529871138926'),  ('岗位管理','/api/job/download','导出','GET','apevolo','2025-08-04 15:00:52.478',0,'163529871138927'),  ('字典管理','/api/dict/create','创建','POST','apevolo','2025-08-04 15:00:52.479',0,'163529871138928'),  ('字典管理','/api/dict/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.479',0,'163529871138929'),  ('字典管理','/api/dict/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.480',0,'163529871138930'),  ('字典管理','/api/dict/query','查询','GET','apevolo','2025-08-04 15:00:52.480',0,'163529871138931'),  ('字典管理','/api/dict/download','导出','GET','apevolo','2025-08-04 15:00:52.481',0,'163529871138932'),  ('字典详情管理','/api/dictdetail/create','创建','POST','apevolo','2025-08-04 15:00:52.481',0,'163529871138933'),  ('字典详情管理','/api/dictdetail/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.482',0,'163529871138934'),  ('字典详情管理','/api/dictdetail/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.482',0,'163529871138935'),  ('字典详情管理','/api/dictdetail/query','查询','GET','apevolo','2025-08-04 15:00:52.483',0,'163529871138936'),  ('作业调度管理','/api/tasks/create','创建','POST','apevolo','2025-08-04 15:00:52.483',0,'163529871138937'),  ('作业调度管理','/api/tasks/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.483',0,'163529871138938'),  ('作业调度管理','/api/tasks/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.484',0,'163529871138939'),  ('作业调度管理','/api/tasks/query','查询','GET','apevolo','2025-08-04 15:00:52.484',0,'163529871138940'),  ('作业调度管理','/api/tasks/download','导出','GET','apevolo','2025-08-04 15:00:52.485',0,'163529871138941'),  ('作业调度管理','/api/tasks/execute','执行','PUT','apevolo','2025-08-04 15:00:52.485',0,'163529871138942'),  ('作业调度管理','/api/tasks/pause','暂停','PUT','apevolo','2025-08-04 15:00:52.486',0,'163529871138943'),  ('作业调度管理','/api/tasks/resume','恢复作业','PUT','apevolo','2025-08-04 15:00:52.486',0,'163529871138944'),  ('作业调度管理','/api/tasks/logs/query','执行日志','GET','apevolo','2025-08-04 15:00:52.487',0,'163529871138945'),  ('作业调度管理','/api/tasks/logs/download','导出','GET','apevolo','2025-08-04 15:00:52.487',0,'163529871138946'),  ('全局设置管理','/api/setting/create','创建','POST','apevolo','2025-08-04 15:00:52.488',0,'163529871138947'),  ('全局设置管理','/api/setting/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.488',0,'163529871138948'),  ('全局设置管理','/api/setting/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.489',0,'163529871138949'),  ('全局设置管理','/api/setting/query','查询','GET','apevolo','2025-08-04 15:00:52.489',0,'163529871138950'),  ('全局设置管理','/api/setting/download','导出','GET','apevolo','2025-08-04 15:00:52.489',0,'163529871138951'),  ('应用密钥管理','/api/appsecret/create','创建','POST','apevolo','2025-08-04 15:00:52.490',0,'163529871138952'),  ('应用密钥管理','/api/appsecret/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.491',0,'163529871138953'),  ('应用密钥管理','/api/appsecret/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.492',0,'163529871138954'),  ('应用密钥管理','/api/appsecret/query','查询','GET','apevolo','2025-08-04 15:00:52.492',0,'163529871138955'),  ('应用密钥管理','/api/appsecret/download','导出','GET','apevolo','2025-08-04 15:00:52.492',0,'163529871138956'),  ('文件存储管理','/api/storage/upload','创建','POST','apevolo','2025-08-04 15:00:52.493',0,'163529871138957'),  ('文件存储管理','/api/storage/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.493',0,'163529871138958'),  ('文件存储管理','/api/storage/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.494',0,'163529871138959'),  ('文件存储管理','/api/storage/query','查询','GET','apevolo','2025-08-04 15:00:52.494',0,'163529871138960'),  ('文件存储管理','/api/storage/download','导出','GET','apevolo','2025-08-04 15:00:52.495',0,'163529871138961'),  ('审计管理','/api/auditing/query','查询','GET','apevolo','2025-08-04 15:00:52.495',0,'163529871138962'),  ('审计管理','/api/auditing/current','用户行为','GET','apevolo','2025-08-04 15:00:52.495',0,'163529871138963'),  ('异常日志管理','/api/exception/query','查询','GET','apevolo','2025-08-04 15:00:52.496',0,'163529871138964'),  ('在线用户管理','/api/online/query','查询','GET','apevolo','2025-08-04 15:00:52.496',0,'***************'),  ('在线用户管理','/api/online/out','强退用户','DELETE','apevolo','2025-08-04 15:00:52.497',0,'***************'),  ('在线用户管理','/api/online/download','导出','GET','apevolo','2025-08-04 15:00:52.497',0,'***************'),  ('服务器管理','/api/service/resources/info','服务器信息','GET','apevolo','2025-08-04 15:00:52.498',0,'***************'),  ('邮箱账户管理','/api/email/account/create','增加','POST','apevolo','2025-08-04 15:00:52.498',0,'***************'),  ('邮箱账户管理','/api/email/account/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.498',0,'***************'),  ('邮箱账户管理','/api/email/account/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.499',0,'***************'),  ('邮箱账户管理','/api/email/account/query','列表','GET','apevolo','2025-08-04 15:00:52.499',0,'***************'),  ('邮箱账户管理','/api/email/account/download','导出','GET','apevolo','2025-08-04 15:00:52.500',0,'***************'),  ('邮件模板管理','/api/email/template/create','创建','POST','apevolo','2025-08-04 15:00:52.500',0,'***************'),  ('邮件模板管理','/api/email/template/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.501',0,'***************'),  ('邮件模板管理','/api/email/template/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.501',0,'***************'),  ('邮件模板管理','/api/email/template/query','查询','GET','apevolo','2025-08-04 15:00:52.502',0,'***************'),  ('邮件队列管理','/api/queued/email/create','创建','POST','apevolo','2025-08-04 15:00:52.502',0,'***************'),  ('邮件队列管理','/api/queued/email/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.502',0,'***************'),  ('邮件队列管理','/api/queued/email/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.503',0,'***************'),  ('邮件队列管理','/api/queued/email/query','查询','GET','apevolo','2025-08-04 15:00:52.504',0,'163529871138981'),  ('角色授权管理','/api/permissions/menus/query','查询菜单','GET','apevolo','2025-08-04 15:00:52.504',0,'163529871138982'),  ('角色授权管理','/api/permissions/apis/query','查询Apis','GET','apevolo','2025-08-04 15:00:52.504',0,'163529871138983'),  ('角色授权管理','/api/permissions/menus/edit','编辑角色菜单','PUT','apevolo','2025-08-04 15:00:52.505',0,'163529871138984'),  ('角色授权管理','/api/permissions/apis/edit','编辑角色Apis','PUT','apevolo','2025-08-04 15:00:52.505',0,'163529871138985'),  ('Apis管理','/api/apis/query','查询','GET','apevolo','2025-08-04 15:00:52.506',0,'163529871138986'),  ('Apis管理','/api/apis/create','创建','POST','apevolo','2025-08-04 15:00:52.506',0,'163529871138987'),  ('Apis管理','/api/apis/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.507',0,'163529871138988'),  ('Apis管理','/api/apis/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.508',0,'163529871138989'),  ('Apis管理','/api/apis/refresh','刷新','POST','apevolo','2025-08-04 15:00:52.508',0,'163529871138990'),  ('租户管理','/api/tenant/query','查询','GET','apevolo','2025-08-04 15:00:52.509',0,'163529871138991'),  ('租户管理','/api/tenant/create','创建','POST','apevolo','2025-08-04 15:00:52.510',0,'163529871138992'),  ('租户管理','/api/tenant/edit','编辑','PUT','apevolo','2025-08-04 15:00:52.510',0,'163529871138993'),  ('租户管理','/api/tenant/delete','删除','DELETE','apevolo','2025-08-04 15:00:52.510',0,'163529871138994'),  ('租户管理','/api/tenant/download','导出','GET','apevolo','2025-08-04 15:00:52.511',0,'163529871138995'),  ('租户管理','/api/tenant/queryAll','查询全部','GET','apevolo','2025-08-04 15:00:52.511',0,'163529871138996'),  ('部门管理','/api/dept/queryTree','获取部门树形数据','GET','apevolo','2025-08-04 15:00:52.512',0,'163529871138997') ;SELECT LAST_INSERT_ROWID();

[耗时]:162.0718ms,请检查并进行优化！


时间:2025-08-04 15:00:52.827
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_role_apis`  (`role_id`,`apis_id`) VALUES('***************','163529871138884'),  ('***************','163529871138885'),  ('***************','163529871138886'),  ('***************','163529871138887'),  ('***************','163529871138888'),  ('***************','163529871138889'),  ('***************','163529871138890'),  ('***************','163529871138891'),  ('***************','163529871138892'),  ('***************','163529871138893'),  ('***************','163529871138894'),  ('***************','163529871138895'),  ('***************','163529871138896'),  ('***************','163529871138897'),  ('***************','163529871138898'),  ('***************','163529871138899'),  ('***************','163529871138900'),  ('***************','163529871138901'),  ('***************','163529871138902'),  ('***************','163529871138903'),  ('***************','163529871138904'),  ('***************','163529871138905'),  ('***************','163529871138906'),  ('***************','163529871138907'),  ('***************','163529871138908'),  ('***************','163529871138909'),  ('***************','163529871138910'),  ('***************','163529871138911'),  ('***************','163529871138912'),  ('***************','163529871138913'),  ('***************','163529871138914'),  ('***************','163529871138915'),  ('***************','163529871138916'),  ('***************','163529871138917'),  ('***************','163529871138918'),  ('***************','163529871138919'),  ('***************','163529871138920'),  ('***************','163529871138921'),  ('***************','163529871138922'),  ('***************','163529871138923'),  ('***************','163529871138924'),  ('***************','163529871138925'),  ('***************','163529871138926'),  ('***************','163529871138927'),  ('***************','163529871138928'),  ('***************','163529871138929'),  ('***************','163529871138930'),  ('***************','163529871138931'),  ('***************','163529871138932'),  ('***************','163529871138933'),  ('***************','163529871138934'),  ('***************','163529871138935'),  ('***************','163529871138936'),  ('***************','163529871138937'),  ('***************','163529871138938'),  ('***************','163529871138939'),  ('***************','163529871138940'),  ('***************','163529871138941'),  ('***************','163529871138942'),  ('***************','163529871138943'),  ('***************','163529871138944'),  ('***************','163529871138945'),  ('***************','163529871138946'),  ('***************','163529871138947'),  ('***************','163529871138948'),  ('***************','163529871138949'),  ('***************','163529871138950'),  ('***************','163529871138951'),  ('***************','163529871138952'),  ('***************','163529871138953'),  ('***************','163529871138954'),  ('***************','163529871138955'),  ('***************','163529871138956'),  ('***************','163529871138957'),  ('***************','163529871138958'),  ('***************','163529871138959'),  ('***************','163529871138960'),  ('***************','163529871138961'),  ('***************','163529871138962'),  ('***************','163529871138963'),  ('***************','163529871138964'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','***************'),  ('***************','163529871138981'),  ('***************','163529871138982'),  ('***************','163529871138983'),  ('***************','163529871138984'),  ('***************','163529871138985'),  ('***************','163529871138986'),  ('***************','163529871138987'),  ('***************','163529871138988'),  ('***************','163529871138989'),  ('***************','163529871138990'),  ('***************','163529871138991'),  ('***************','163529871138992'),  ('***************','163529871138993'),  ('***************','163529871138994'),  ('***************','163529871138995'),  ('***************','163529871138996'),  ('***************','163529871138997'),  ('***************','163529871138884'),  ('***************','163529871138885'),  ('***************','163529871138886'),  ('***************','163529871138887'),  ('***************','163529871138888'),  ('***************','163529871138889'),  ('***************','163529871138893'),  ('***************','163529871138894'),  ('***************','163529871138895'),  ('***************','163529871138896'),  ('***************','163529871138897'),  ('***************','163529871138910'),  ('***************','163529871138912'),  ('***************','163529871138916'),  ('***************','163529871138925'),  ('***************','163529871138926'),  ('***************','163529871138936'),  ('***************','163529871138963') ;SELECT LAST_INSERT_ROWID();

[耗时]:122.5011ms,请检查并进行优化！


时间:2025-08-04 15:00:53.023
所在类:
等级:Warning
信息:执行DB--> 操作用户:[] 操作类型:[Insert] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:INSERT INTO `sys_tenant`  (`tenant_id`,`name`,`description`,`tenant_type`,`config_id`,`db_type`,`connection_string`,`create_by`,`create_time`,`is_deleted`,`id`) VALUES('1001','测试租户1','测试租户1','2','apevolo_tenant_1001','2','apevolo_tenant_1001.db','apevolo','2025-08-04 15:00:52.853',0,'163519427764306'),  ('1002','测试租户2','测试租户2','2','apevolo_tenant_1002','2','apevolo_tenant_1002.db','apevolo','2025-08-04 15:00:52.860',0,'163519427764307') ;SELECT LAST_INSERT_ROWID();

[耗时]:159.4372ms,请检查并进行优化！


时间:2025-08-04 15:05:14.281
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:7.3966ms


时间:2025-08-04 15:05:14.310
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_role`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3662ms


时间:2025-08-04 15:05:14.313
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_menu`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3037ms


时间:2025-08-04 15:05:14.316
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_department`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2997ms


时间:2025-08-04 15:05:14.320
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_job`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.383ms


时间:2025-08-04 15:05:14.322
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_setting`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3113ms


时间:2025-08-04 15:05:14.325
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_dict`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.333ms


时间:2025-08-04 15:05:14.328
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_dict_detail`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3094ms


时间:2025-08-04 15:05:14.331
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_quartz_job`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.312ms


时间:2025-08-04 15:05:14.335
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `email_account`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.5619ms


时间:2025-08-04 15:05:14.338
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `email_message_template`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3239ms


时间:2025-08-04 15:05:14.340
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_user_role`     LIMIT 0,1
[耗时]:0.3647ms


时间:2025-08-04 15:05:14.343
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_user_job`     LIMIT 0,1
[耗时]:0.2648ms


时间:2025-08-04 15:05:14.345
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_role_menu`     LIMIT 0,1
[耗时]:0.2474ms


时间:2025-08-04 15:05:14.348
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_apis`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3499ms


时间:2025-08-04 15:05:14.352
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_role_apis`     LIMIT 0,1
[耗时]:0.3945ms


时间:2025-08-04 15:05:14.356
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_tenant`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4032ms


时间:2025-08-04 15:05:15.087
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `task_name`,`task_group`,`cron`,`assembly_name`,`class_name`,`description`,`principal`,`alert_email`,`pause_after_failure`,`run_times`,`start_time`,`end_time`,`trigger_type`,`interval_second`,`cycle_run_times`,`is_enable`,`run_params`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_quartz_job`  WHERE ( `is_deleted` = @IsDeleted0 )  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4388ms


时间:2025-08-04 15:06:45.045
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `access_token`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_token_blacklist`   WHERE ( `access_token` = @AccessToken0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@AccessToken0 [Value]:56abb002b6e99683 [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:3.7163ms


时间:2025-08-04 15:06:45.055
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3687ms


时间:2025-08-04 15:06:45.064
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:06:45.068
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = ***************    
[耗时]:0ms


时间:2025-08-04 15:06:45.070
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = ***************     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:06:45.102
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = ***************    
[耗时]:0ms


时间:2025-08-04 15:06:45.125
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (***************,***************)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:06:45.716
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:*************** [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3653ms


时间:2025-08-04 15:06:45.718
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:06:45.722
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = ***************    
[耗时]:0ms


时间:2025-08-04 15:06:45.724
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = ***************     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:06:45.726
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = ***************    
[耗时]:0ms


时间:2025-08-04 15:06:45.727
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (***************,***************)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:06:45.737
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `m`.`permission` FROM `sys_user_role` `ur`  ,`sys_role_menu`  `rm` ,`sys_menu`  `m`  WHERE (( `ur`.`role_id` = `rm`.`role_id` ) AND ( `rm`.`menu_id` = `m`.`id` ))  AND ((( `ur`.`user_id` = @UserId0 ) AND ( `m`.`type` <> @Type1 )) AND ( `m`.`permission` IS NOT NULL ))  AND ( `m`.`is_deleted` = @IsDeleted3 )GROUP BY `m`.`permission` ORDER BY `m`.`permission` ASC  
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Type1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[耗时]:1.2716ms


时间:2025-08-04 15:06:45.860
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT  DISTINCT  `m`.`title` AS `Title` , `m`.`path` AS `Path` , `m`.`permission` AS `Permission` , `m`.`i_frame` AS `IFrame` , `m`.`component` AS `Component` , `m`.`component_name` AS `ComponentName` , `m`.`parent_id` AS `ParentId` , `m`.`sort` AS `Sort` , `m`.`icon` AS `Icon` , `m`.`type` AS `Type` , `m`.`is_deleted` AS `IsDeleted` , `m`.`id` AS `Id` , `m`.`create_time` AS `CreateTime` , `m`.`create_by` AS `CreateBy` , `m`.`cache` AS `Cache` , `m`.`hidden` AS `Hidden`  FROM `sys_user_role` `ur`  ,`sys_role_menu`  `rm` ,`sys_menu`  `m`  WHERE (( `ur`.`role_id` = `rm`.`role_id` ) AND ( `rm`.`menu_id` = `m`.`id` ))  AND (( `ur`.`user_id` = @UserId0 ) AND ( `m`.`type` <> @Type1 ))  AND ( `m`.`is_deleted` = @IsDeleted2 )ORDER BY `m`.`sort` ASC  
[Pars]:
[Name]:@UserId0 [Value]:*************** [Type]:Int64    
[Name]:@Type1 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[耗时]:1.0657ms


时间:2025-08-04 15:06:45.882
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`value`,`enabled`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_setting`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:IsAuditLogSaveDB [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.356ms


时间:2025-08-04 15:06:48.004
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:dept_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.4345ms


时间:2025-08-04 15:06:48.015
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:1.9488ms


时间:2025-08-04 15:06:48.018
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.5215ms


时间:2025-08-04 15:06:52.477
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4141ms


时间:2025-08-04 15:06:52.480
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4242ms


时间:2025-08-04 15:06:57.917
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4192ms


时间:2025-08-04 15:06:57.919
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3643ms


时间:2025-08-04 15:07:02.873
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditenabled0 [Value]:True [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3404ms


时间:2025-08-04 15:07:02.876
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditenabled0 [Value]:True [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4812ms


时间:2025-08-04 15:07:05.098
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  
[Pars]:
[Name]:@Conditparent_id0 [Value]:163519427764312 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3402ms


时间:2025-08-04 15:07:09.681
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.325ms


时间:2025-08-04 15:07:09.682
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.333ms


时间:2025-08-04 15:07:11.511
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  
[Pars]:
[Name]:@Conditparent_id0 [Value]:163519427764312 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3235ms


时间:2025-08-04 15:07:15.204
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764314 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3118ms


时间:2025-08-04 15:07:15.231
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE (( `parent_id` = @ParentId0 ) AND  ( `enabled`=1 ) )  AND ( `is_deleted` = @IsDeleted1 )  
[Pars]:
[Name]:@ParentId0 [Value]:163519427764312 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3261ms


时间:2025-08-04 15:07:15.233
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764312 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.2732ms


时间:2025-08-04 15:07:15.236
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE (( `parent_id` = @ParentId0 ) AND  ( `enabled`=1 ) )  AND ( `is_deleted` = @IsDeleted1 )  
[Pars]:
[Name]:@ParentId0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.2546ms

