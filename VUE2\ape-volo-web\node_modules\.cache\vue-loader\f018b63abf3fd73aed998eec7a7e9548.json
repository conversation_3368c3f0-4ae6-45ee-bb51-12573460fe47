{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\test\\MousewheelTest.vue?vue&type=template&id=4730e61b&scoped=true", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\test\\MousewheelTest.vue", "mtime": 1754291968172}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754288975174}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754288946893}], "contextDependencies": [], "result": ["\n<div class=\"mousewheel-test\">\n  <el-card>\n    <div slot=\"header\">\n      <span>Element UI Mousewheel 修复测试</span>\n      <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"checkStatus\">检查修复状态</el-button>\n    </div>\n    \n    <div class=\"test-content\">\n      <h3>测试说明</h3>\n      <p>这个页面用于测试 Element UI mousewheel 事件的修复效果。打开浏览器开发者工具的控制台，然后滚动下面的组件，观察是否还有性能警告。</p>\n      \n      <div class=\"test-sections\">\n        <!-- 表格滚动测试 -->\n        <el-card class=\"test-card\">\n          <div slot=\"header\">表格滚动测试</div>\n          <el-table\n            :data=\"tableData\"\n            height=\"200\"\n            style=\"width: 100%\">\n            <el-table-column prop=\"date\" label=\"日期\" width=\"180\"></el-table-column>\n            <el-table-column prop=\"name\" label=\"姓名\" width=\"180\"></el-table-column>\n            <el-table-column prop=\"address\" label=\"地址\"></el-table-column>\n          </el-table>\n        </el-card>\n        \n        <!-- 下拉框测试 -->\n        <el-card class=\"test-card\">\n          <div slot=\"header\">下拉框测试</div>\n          <el-select v-model=\"selectedValue\" placeholder=\"请选择\">\n            <el-option\n              v-for=\"item in options\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\">\n            </el-option>\n          </el-select>\n        </el-card>\n        \n        <!-- 滚动条测试 -->\n        <el-card class=\"test-card\">\n          <div slot=\"header\">滚动条测试</div>\n          <el-scrollbar style=\"height: 200px;\">\n            <div style=\"height: 500px; padding: 20px;\">\n              <p v-for=\"i in 20\" :key=\"i\">这是第 {{ i }} 行内容，用于测试滚动性能。</p>\n            </div>\n          </el-scrollbar>\n        </el-card>\n        \n        <!-- 日期选择器测试 -->\n        <el-card class=\"test-card\">\n          <div slot=\"header\">日期选择器测试</div>\n          <el-date-picker\n            v-model=\"selectedDate\"\n            type=\"date\"\n            placeholder=\"选择日期\">\n          </el-date-picker>\n        </el-card>\n      </div>\n      \n      <div class=\"status-info\">\n        <h3>修复状态</h3>\n        <ul>\n          <li v-for=\"status in fixStatus\" :key=\"status\" v-html=\"status\"></li>\n        </ul>\n      </div>\n    </div>\n  </el-card>\n</div>\n", null]}