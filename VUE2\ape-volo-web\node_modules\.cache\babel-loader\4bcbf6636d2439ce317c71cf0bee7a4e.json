{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\main.js", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\main.js", "mtime": 1754291643812}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\babel.config.js", "mtime": 1754278696306}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754288923956}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\eslint-loader\\index.js", "mtime": 1754288859365}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nrequire(\"D:\\\\word\\\\ape-volo\\\\VUE2\\\\ape-volo-web\\\\node_modules\\\\core-js\\\\modules\\\\es6.array.iterator.js\");\nrequire(\"D:\\\\word\\\\ape-volo\\\\VUE2\\\\ape-volo-web\\\\node_modules\\\\core-js\\\\modules\\\\es6.promise.js\");\nrequire(\"D:\\\\word\\\\ape-volo\\\\VUE2\\\\ape-volo-web\\\\node_modules\\\\core-js\\\\modules\\\\es6.object.assign.js\");\nrequire(\"D:\\\\word\\\\ape-volo\\\\VUE2\\\\ape-volo-web\\\\node_modules\\\\core-js\\\\modules\\\\es7.promise.finally.js\");\nvar _vue = _interopRequireDefault(require(\"vue\"));\nvar _jsCookie = _interopRequireDefault(require(\"js-cookie\"));\nrequire(\"normalize.css/normalize.css\");\nvar _elementUi = _interopRequireDefault(require(\"element-ui\"));\nvar _elementUIOptimization = _interopRequireDefault(require(\"@/utils/elementUIOptimization\"));\nvar _echartsOptimization = _interopRequireDefault(require(\"@/utils/echartsOptimization\"));\nvar _mavonEditor = _interopRequireDefault(require(\"mavon-editor\"));\nrequire(\"mavon-editor/dist/css/index.css\");\nvar _Dict = _interopRequireDefault(require(\"./components/Dict\"));\nvar _permission = _interopRequireDefault(require(\"@/utils/permission\"));\nvar _Permission = _interopRequireDefault(require(\"./components/Permission\"));\nrequire(\"./assets/styles/element-variables.scss\");\nrequire(\"./assets/styles/index.scss\");\nvar _vueHighlightjs = _interopRequireDefault(require(\"vue-highlightjs\"));\nrequire(\"highlight.js/styles/atom-one-dark.css\");\nvar _App = _interopRequireDefault(require(\"./App\"));\nvar _store = _interopRequireDefault(require(\"./store\"));\nvar _routers = _interopRequireDefault(require(\"./router/routers\"));\nrequire(\"./assets/icons\");\nrequire(\"./router/index\");\nrequire(\"echarts-gl\");\n// Element UI 性能优化\n\n// ECharts 性能优化\n\n//\n\n// 数据字典\n\n// 权限指令\n\n// global css\n\n// 代码高亮\n\n// icon\n// permission control\n\n_vue.default.use(_permission.default);\n_vue.default.use(_vueHighlightjs.default);\n_vue.default.use(_mavonEditor.default);\n_vue.default.use(_Permission.default);\n_vue.default.use(_Dict.default);\n_vue.default.use(_elementUi.default, {\n  size: _jsCookie.default.get('size') || 'small' // set element-ui default size\n});\n\n// 使用 Element UI 性能优化插件\n_vue.default.use(_elementUIOptimization.default);\n\n// 使用 ECharts 性能优化插件\n_vue.default.use(_echartsOptimization.default);\n_vue.default.config.productionTip = false;\nnew _vue.default({\n  el: '#app',\n  router: _routers.default,\n  store: _store.default,\n  render: function render(h) {\n    return h(_App.default);\n  }\n});", null]}