﻿
时间:2025-08-04 15:06:45.394
所在类:
等级:Warning
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`value`,`enabled`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_setting`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:IsAdminNotAuthentication [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:22.709ms,请检查并进行优化！


时间:2025-08-04 15:06:45.449
所在类:
等级:Warning
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT  `a`.`url` AS `Url` , `a`.`method` AS `Method`  FROM `sys_user_role` `ur`  ,`sys_role_apis`  `ra` ,`sys_apis`  `a`  WHERE (( `ur`.`role_id` = `ra`.`role_id` ) AND ( `ra`.`apis_id` = `a`.`id` ))  AND ( `ur`.`user_id` = @UserId0 )  AND ( `a`.`is_deleted` = @IsDeleted1 )GROUP BY `a`.`url`,`a`.`method` ORDER BY `a`.`url` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:16.6399ms,请检查并进行优化！


时间:2025-08-04 15:06:48.032
所在类:
等级:Warning
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_id`,`label`,`value`,`dict_sort`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict_detail`  WHERE ( `dict_id` = @DictId0 )  AND ( `is_deleted` = @IsDeleted1 )ORDER BY `dict_sort` ASC  
[Pars]:
[Name]:@DictId0 [Value]:163519427764331 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:17.8826ms,请检查并进行优化！


时间:2025-08-04 15:08:50.108
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:5.6182ms


时间:2025-08-04 15:08:50.156
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_role`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.348ms


时间:2025-08-04 15:08:50.161
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_menu`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3195ms


时间:2025-08-04 15:08:50.166
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_department`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3574ms


时间:2025-08-04 15:08:50.171
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_job`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.303ms


时间:2025-08-04 15:08:50.177
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_setting`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3582ms


时间:2025-08-04 15:08:50.183
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_dict`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4038ms


时间:2025-08-04 15:08:50.188
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_dict_detail`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2938ms


时间:2025-08-04 15:08:50.194
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_quartz_job`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4606ms


时间:2025-08-04 15:08:50.201
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `email_account`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3056ms


时间:2025-08-04 15:08:50.206
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `email_message_template`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3031ms


时间:2025-08-04 15:08:50.212
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_user_role`     LIMIT 0,1
[耗时]:0.3807ms


时间:2025-08-04 15:08:50.219
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_user_job`     LIMIT 0,1
[耗时]:0.3376ms


时间:2025-08-04 15:08:50.224
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_role_menu`     LIMIT 0,1
[耗时]:0.2767ms


时间:2025-08-04 15:08:50.233
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_apis`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3901ms


时间:2025-08-04 15:08:50.237
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_role_apis`     LIMIT 0,1
[耗时]:0.3325ms


时间:2025-08-04 15:08:50.244
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_tenant`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3509ms


时间:2025-08-04 15:08:50.971
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `task_name`,`task_group`,`cron`,`assembly_name`,`class_name`,`description`,`principal`,`alert_email`,`pause_after_failure`,`run_times`,`start_time`,`end_time`,`trigger_type`,`interval_second`,`cycle_run_times`,`is_enable`,`run_params`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_quartz_job`  WHERE ( `is_deleted` = @IsDeleted0 )  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3428ms

