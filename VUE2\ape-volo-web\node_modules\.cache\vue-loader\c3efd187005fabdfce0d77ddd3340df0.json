{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\test\\MousewheelTest.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\test\\MousewheelTest.vue", "mtime": 1754292344441}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754288923956}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754288946893}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'MousewheelTest',\n  data() {\n    return {\n      selectedValue: '',\n      selectedDate: '',\n      fixStatus: [],\n      tableData: [],\n      options: [\n        { value: '1', label: '选项1' },\n        { value: '2', label: '选项2' },\n        { value: '3', label: '选项3' },\n        { value: '4', label: '选项4' },\n        { value: '5', label: '选项5' },\n        { value: '6', label: '选项6' },\n        { value: '7', label: '选项7' },\n        { value: '8', label: '选项8' },\n        { value: '9', label: '选项9' },\n        { value: '10', label: '选项10' }\n      ]\n    }\n  },\n  mounted() {\n    this.generateTableData()\n    this.checkStatus()\n  },\n  methods: {\n    generateTableData() {\n      // 生成大量表格数据用于测试滚动\n      for (let i = 0; i < 100; i++) {\n        this.tableData.push({\n          date: `2024-01-${String(i % 30 + 1).padStart(2, '0')}`,\n          name: `用户${i + 1}`,\n          address: `地址${i + 1} - 这是一个很长的地址用于测试表格滚动性能`\n        })\n      }\n    },\n    checkStatus() {\n      // 检查修复状态\n      if (this.$checkElementUIFixes) {\n        this.fixStatus = this.$checkElementUIFixes()\n      } else {\n        this.fixStatus = ['❌ 修复检查方法不可用']\n      }\n    }\n  }\n}\n", null]}