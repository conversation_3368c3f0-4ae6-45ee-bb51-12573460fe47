{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\test\\MousewheelTest.vue?vue&type=template&id=4730e61b&scoped=true", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\test\\MousewheelTest.vue", "mtime": 1754292322701}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754288975174}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754288946893}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"mousewheel-test\" },\n    [\n      _c(\"el-card\", [\n        _c(\n          \"div\",\n          { attrs: { slot: \"header\" }, slot: \"header\" },\n          [\n            _c(\"span\", [_vm._v(\"Element UI Mousewheel 修复测试\")]),\n            _vm._v(\" \"),\n            _c(\n              \"el-button\",\n              {\n                staticStyle: { float: \"right\", padding: \"3px 0\" },\n                attrs: { type: \"text\" },\n                on: { click: _vm.checkStatus },\n              },\n              [_vm._v(\"检查修复状态\")]\n            ),\n          ],\n          1\n        ),\n        _vm._v(\" \"),\n        _c(\"div\", { staticClass: \"test-content\" }, [\n          _c(\"h3\", [_vm._v(\"测试说明\")]),\n          _vm._v(\" \"),\n          _c(\"p\", [\n            _vm._v(\n              \"这个页面用于测试 Element UI mousewheel 事件的修复效果。打开浏览器开发者工具的控制台，然后滚动下面的组件，观察是否还有性能警告。\"\n            ),\n          ]),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"test-sections\" },\n            [\n              _c(\n                \"el-card\",\n                { staticClass: \"test-card\" },\n                [\n                  _c(\"div\", { attrs: { slot: \"header\" }, slot: \"header\" }, [\n                    _vm._v(\"表格滚动测试\"),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-table\",\n                    {\n                      staticStyle: { width: \"100%\" },\n                      attrs: { data: _vm.tableData, height: \"200\" },\n                    },\n                    [\n                      _c(\"el-table-column\", {\n                        attrs: { prop: \"date\", label: \"日期\", width: \"180\" },\n                      }),\n                      _vm._v(\" \"),\n                      _c(\"el-table-column\", {\n                        attrs: { prop: \"name\", label: \"姓名\", width: \"180\" },\n                      }),\n                      _vm._v(\" \"),\n                      _c(\"el-table-column\", {\n                        attrs: { prop: \"address\", label: \"地址\" },\n                      }),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-card\",\n                { staticClass: \"test-card\" },\n                [\n                  _c(\"div\", { attrs: { slot: \"header\" }, slot: \"header\" }, [\n                    _vm._v(\"下拉框测试\"),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-select\",\n                    {\n                      attrs: { placeholder: \"请选择\" },\n                      model: {\n                        value: _vm.selectedValue,\n                        callback: function ($$v) {\n                          _vm.selectedValue = $$v\n                        },\n                        expression: \"selectedValue\",\n                      },\n                    },\n                    _vm._l(_vm.options, function (item) {\n                      return _c(\"el-option\", {\n                        key: item.value,\n                        attrs: { label: item.label, value: item.value },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-card\",\n                { staticClass: \"test-card\" },\n                [\n                  _c(\"div\", { attrs: { slot: \"header\" }, slot: \"header\" }, [\n                    _vm._v(\"滚动条测试\"),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"el-scrollbar\", { staticStyle: { height: \"200px\" } }, [\n                    _c(\n                      \"div\",\n                      { staticStyle: { height: \"500px\", padding: \"20px\" } },\n                      _vm._l(20, function (i) {\n                        return _c(\"p\", { key: i }, [\n                          _vm._v(\n                            \"这是第 \" +\n                              _vm._s(i) +\n                              \" 行内容，用于测试滚动性能。\"\n                          ),\n                        ])\n                      }),\n                      0\n                    ),\n                  ]),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-card\",\n                { staticClass: \"test-card\" },\n                [\n                  _c(\"div\", { attrs: { slot: \"header\" }, slot: \"header\" }, [\n                    _vm._v(\"日期选择器测试\"),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"el-date-picker\", {\n                    attrs: { type: \"date\", placeholder: \"选择日期\" },\n                    model: {\n                      value: _vm.selectedDate,\n                      callback: function ($$v) {\n                        _vm.selectedDate = $$v\n                      },\n                      expression: \"selectedDate\",\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\"div\", { staticClass: \"status-info\" }, [\n            _c(\"h3\", [_vm._v(\"修复状态\")]),\n            _vm._v(\" \"),\n            _c(\n              \"ul\",\n              _vm._l(_vm.fixStatus, function (status) {\n                return _c(\"li\", {\n                  key: status,\n                  domProps: { innerHTML: _vm._s(status) },\n                })\n              }),\n              0\n            ),\n          ]),\n        ]),\n      ]),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}