﻿
时间:2025-08-04 15:00:45.415
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.263ms


时间:2025-08-04 15:00:45.444
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_app_secret"(
"id" bigint NOT NULL PRIMARY KEY ,
"app_id" varchar(255) NOT NULL  ,
"app_secret_key" varchar(255) NOT NULL  ,
"app_name" varchar(255) NOT NULL  ,
"remark" varchar(255) NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:0.3043ms


时间:2025-08-04 15:00:45.449
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_app_secret_CreateBy'
[耗时]:1.6337ms


时间:2025-08-04 15:00:45.454
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_app_secret_CreateBy ON `sys_app_secret`(`create_by` Asc)
[耗时]:0.1684ms


时间:2025-08-04 15:00:45.455
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_app_secret_IsDeleted'
[耗时]:0.0842ms


时间:2025-08-04 15:00:45.456
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_app_secret_IsDeleted ON `sys_app_secret`(`is_deleted` Asc)
[耗时]:0.1106ms


时间:2025-08-04 15:00:45.624
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0628ms


时间:2025-08-04 15:00:45.649
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_file_record"(
"id" bigint NOT NULL PRIMARY KEY ,
"description" varchar(255) NOT NULL  ,
"content_type" varchar(255) NULL  ,
"content_type_name" varchar(255) NULL  ,
"content_type_name_en" varchar(255) NULL  ,
"original_name" varchar(255) NULL  ,
"new_name" varchar(255) NULL  ,
"file_path" varchar(255) NULL  ,
"size" varchar(255) NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:1.7038ms


时间:2025-08-04 15:00:45.650
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_file_record_CreateBy'
[耗时]:0.0901ms


时间:2025-08-04 15:00:45.650
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_file_record_CreateBy ON `sys_file_record`(`create_by` Asc)
[耗时]:0.1279ms


时间:2025-08-04 15:00:45.651
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_file_record_IsDeleted'
[耗时]:0.0519ms


时间:2025-08-04 15:00:45.651
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_file_record_IsDeleted ON `sys_file_record`(`is_deleted` Asc)
[耗时]:0.0986ms


时间:2025-08-04 15:00:45.764
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0424ms


时间:2025-08-04 15:00:45.767
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_setting"(
"id" bigint NOT NULL PRIMARY KEY ,
"name" varchar(255) NOT NULL  ,
"value" varchar(255) NOT NULL  ,
"enabled" bit NOT NULL  ,
"description" varchar(255) NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:1.5412ms


时间:2025-08-04 15:00:45.768
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_setting_CreateBy'
[耗时]:0.0665ms


时间:2025-08-04 15:00:45.768
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_setting_CreateBy ON `sys_setting`(`create_by` Asc)
[耗时]:0.1146ms


时间:2025-08-04 15:00:45.769
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_setting_IsDeleted'
[耗时]:0.0499ms


时间:2025-08-04 15:00:45.769
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_setting_IsDeleted ON `sys_setting`(`is_deleted` Asc)
[耗时]:0.1159ms


时间:2025-08-04 15:00:45.909
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.1172ms


时间:2025-08-04 15:00:45.913
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_tenant"(
"id" bigint NOT NULL PRIMARY KEY ,
"tenant_id" integer NOT NULL  ,
"name" varchar(255) NOT NULL  ,
"description" varchar(255) NULL  ,
"tenant_type" integer NOT NULL  ,
"config_id" varchar(255) NULL  ,
"db_type" integer NULL  ,
"connection_string" varchar(255) NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:1.9118ms


时间:2025-08-04 15:00:45.914
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_tenant_CreateBy'
[耗时]:0.121ms


时间:2025-08-04 15:00:45.915
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_tenant_CreateBy ON `sys_tenant`(`create_by` Asc)
[耗时]:0.165ms


时间:2025-08-04 15:00:45.915
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_tenant_IsDeleted'
[耗时]:0.0755ms


时间:2025-08-04 15:00:45.916
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_tenant_IsDeleted ON `sys_tenant`(`is_deleted` Asc)
[耗时]:0.1159ms


时间:2025-08-04 15:00:46.067
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.064ms


时间:2025-08-04 15:00:46.070
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_token_blacklist"(
"id" bigint NOT NULL PRIMARY KEY ,
"access_token" varchar(255) NOT NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:2.2601ms


时间:2025-08-04 15:00:46.071
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_token_blacklist_CreateBy'
[耗时]:0.0684ms


时间:2025-08-04 15:00:46.072
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_token_blacklist_CreateBy ON `sys_token_blacklist`(`create_by` Asc)
[耗时]:0.1353ms


时间:2025-08-04 15:00:46.073
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_token_blacklist_IsDeleted'
[耗时]:0.104ms


时间:2025-08-04 15:00:46.075
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_token_blacklist_IsDeleted ON `sys_token_blacklist`(`is_deleted` Asc)
[耗时]:1.2263ms


时间:2025-08-04 15:00:46.189
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0421ms


时间:2025-08-04 15:00:46.193
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_quartz_job"(
"id" bigint NOT NULL PRIMARY KEY ,
"task_name" varchar(255) NOT NULL  ,
"task_group" varchar(255) NOT NULL  ,
"cron" varchar(255) NULL  ,
"assembly_name" varchar(255) NOT NULL  ,
"class_name" varchar(255) NOT NULL  ,
"description" varchar(500) NULL  ,
"principal" varchar(255) NULL  ,
"alert_email" varchar(255) NULL  ,
"pause_after_failure" bit NOT NULL  ,
"run_times" integer NOT NULL  ,
"start_time" datetime NULL  ,
"end_time" datetime NULL  ,
"trigger_type" integer NOT NULL  ,
"interval_second" integer NOT NULL  ,
"cycle_run_times" integer NOT NULL  ,
"is_enable" bit NOT NULL  ,
"run_params" varchar(255) NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:2.5512ms


时间:2025-08-04 15:00:46.194
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_quartz_job_CreateBy'
[耗时]:0.0783ms


时间:2025-08-04 15:00:46.196
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_quartz_job_CreateBy ON `sys_quartz_job`(`create_by` Asc)
[耗时]:0.5556ms


时间:2025-08-04 15:00:46.197
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_quartz_job_IsDeleted'
[耗时]:0.0749ms


时间:2025-08-04 15:00:46.198
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_quartz_job_IsDeleted ON `sys_quartz_job`(`is_deleted` Asc)
[耗时]:0.1316ms


时间:2025-08-04 15:00:46.353
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.057ms


时间:2025-08-04 15:00:46.358
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_quartz_job_log"(
"id" bigint NOT NULL PRIMARY KEY ,
"task_id" bigint NOT NULL  ,
"task_name" varchar(255) NOT NULL  ,
"task_group" varchar(255) NOT NULL  ,
"assembly_name" varchar(255) NOT NULL  ,
"class_name" varchar(255) NOT NULL  ,
"cron" varchar(255) NULL  ,
"exception_detail" varchar(255) NULL  ,
"execution_duration" bigint NOT NULL  ,
"run_params" varchar(255) NULL  ,
"is_success" bit NOT NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:2.7028ms


时间:2025-08-04 15:00:46.359
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_quartz_job_log_CreateBy'
[耗时]:0.0895ms


时间:2025-08-04 15:00:46.360
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_quartz_job_log_CreateBy ON `sys_quartz_job_log`(`create_by` Asc)
[耗时]:0.1424ms


时间:2025-08-04 15:00:46.360
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_quartz_job_log_IsDeleted'
[耗时]:0.0661ms


时间:2025-08-04 15:00:46.361
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_quartz_job_log_IsDeleted ON `sys_quartz_job_log`(`is_deleted` Asc)
[耗时]:0.1289ms


时间:2025-08-04 15:00:46.487
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0535ms


时间:2025-08-04 15:00:46.491
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_dict"(
"id" bigint NOT NULL PRIMARY KEY ,
"dict_type" integer NOT NULL  ,
"name" varchar(255) NOT NULL  ,
"description" varchar(255) NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:2.0051ms


时间:2025-08-04 15:00:46.492
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_dict_CreateBy'
[耗时]:0.085ms


时间:2025-08-04 15:00:46.493
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_dict_CreateBy ON `sys_dict`(`create_by` Asc)
[耗时]:0.1389ms


时间:2025-08-04 15:00:46.493
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_dict_IsDeleted'
[耗时]:0.0682ms


时间:2025-08-04 15:00:46.494
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_dict_IsDeleted ON `sys_dict`(`is_deleted` Asc)
[耗时]:0.1061ms


时间:2025-08-04 15:00:46.638
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.059ms


时间:2025-08-04 15:00:46.644
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_dict_detail"(
"id" bigint NOT NULL PRIMARY KEY ,
"dict_id" bigint NOT NULL  ,
"label" varchar(255) NOT NULL  ,
"value" varchar(255) NOT NULL  ,
"dict_sort" integer NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:5.3431ms


时间:2025-08-04 15:00:46.667
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_dict_detail_CreateBy'
[耗时]:0.1423ms


时间:2025-08-04 15:00:46.667
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_dict_detail_CreateBy ON `sys_dict_detail`(`create_by` Asc)
[耗时]:0.1632ms


时间:2025-08-04 15:00:46.669
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_dict_detail_IsDeleted'
[耗时]:0.0914ms


时间:2025-08-04 15:00:46.670
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_dict_detail_IsDeleted ON `sys_dict_detail`(`is_deleted` Asc)
[耗时]:0.1324ms


时间:2025-08-04 15:00:46.792
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.1785ms


时间:2025-08-04 15:00:46.795
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "queued_email"(
"id" bigint NOT NULL PRIMARY KEY ,
"from" varchar(255) NOT NULL  ,
"from_name" varchar(255) NULL  ,
"to" varchar(255) NOT NULL  ,
"to_name" varchar(255) NULL  ,
"reply_to" varchar(255) NULL  ,
"reply_to_name" varchar(255) NULL  ,
"priority" integer NOT NULL  ,
"cc" varchar(255) NULL  ,
"bcc" varchar(255) NULL  ,
"subject" varchar(255) NULL  ,
"body" varchar(4000) NOT NULL  ,
"sent_tries" integer NOT NULL  ,
"is_send" bit NULL  ,
"send_time" datetime NULL  ,
"email_account_id" bigint NOT NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:1.95ms


时间:2025-08-04 15:00:46.795
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_queued_email_CreateBy'
[耗时]:0.0637ms


时间:2025-08-04 15:00:46.796
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_queued_email_CreateBy ON `queued_email`(`create_by` Asc)
[耗时]:0.1225ms


时间:2025-08-04 15:00:46.796
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_queued_email_IsDeleted'
[耗时]:0.0424ms


时间:2025-08-04 15:00:46.797
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_queued_email_IsDeleted ON `queued_email`(`is_deleted` Asc)
[耗时]:0.1009ms


时间:2025-08-04 15:00:46.923
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0592ms


时间:2025-08-04 15:00:46.927
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_apis"(
"id" bigint NOT NULL PRIMARY KEY ,
"group" varchar(255) NOT NULL  ,
"url" varchar(255) NOT NULL  ,
"description" varchar(255) NULL  ,
"method" varchar(255) NOT NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:2.0864ms


时间:2025-08-04 15:00:46.927
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_apis_CreateBy'
[耗时]:0.0678ms


时间:2025-08-04 15:00:46.928
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_apis_CreateBy ON `sys_apis`(`create_by` Asc)
[耗时]:0.1368ms


时间:2025-08-04 15:00:46.928
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_apis_IsDeleted'
[耗时]:0.0634ms


时间:2025-08-04 15:00:46.929
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_apis_IsDeleted ON `sys_apis`(`is_deleted` Asc)
[耗时]:0.1244ms


时间:2025-08-04 15:00:47.051
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0628ms


时间:2025-08-04 15:00:47.054
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_department"(
"id" bigint NOT NULL PRIMARY KEY ,
"name" varchar(255) NOT NULL  ,
"parent_id" bigint NOT NULL  ,
"sort" integer NULL  ,
"enabled" bit NULL  ,
"sub_count" integer NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:2.0717ms


时间:2025-08-04 15:00:47.054
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_department_CreateBy'
[耗时]:0.0771ms


时间:2025-08-04 15:00:47.055
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_department_CreateBy ON `sys_department`(`create_by` Asc)
[耗时]:0.1349ms


时间:2025-08-04 15:00:47.055
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_department_IsDeleted'
[耗时]:0.0588ms


时间:2025-08-04 15:00:47.056
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_department_IsDeleted ON `sys_department`(`is_deleted` Asc)
[耗时]:0.1471ms


时间:2025-08-04 15:00:47.196
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0574ms


时间:2025-08-04 15:00:47.200
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_job"(
"id" bigint NOT NULL PRIMARY KEY ,
"name" varchar(255) NOT NULL  ,
"sort" integer NULL  ,
"enabled" bit NOT NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:2.6547ms


时间:2025-08-04 15:00:47.201
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_job_CreateBy'
[耗时]:0.0677ms


时间:2025-08-04 15:00:47.202
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_job_CreateBy ON `sys_job`(`create_by` Asc)
[耗时]:0.1931ms


时间:2025-08-04 15:00:47.202
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_job_IsDeleted'
[耗时]:0.0565ms


时间:2025-08-04 15:00:47.203
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_job_IsDeleted ON `sys_job`(`is_deleted` Asc)
[耗时]:0.1192ms


时间:2025-08-04 15:00:47.428
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0874ms


时间:2025-08-04 15:00:47.431
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_menu"(
"id" bigint NOT NULL PRIMARY KEY ,
"title" varchar(255) NOT NULL  ,
"path" varchar(255) NULL  ,
"permission" varchar(255) NULL  ,
"i_frame" bit NOT NULL  ,
"component" varchar(255) NULL  ,
"component_name" varchar(255) NULL  ,
"parent_id" bigint NOT NULL  ,
"sort" integer NULL  ,
"icon" varchar(255) NULL  ,
"type" integer NOT NULL  ,
"cache" bit NULL  ,
"hidden" bit NULL  ,
"sub_count" integer NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:1.9863ms


时间:2025-08-04 15:00:47.432
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_menu_CreateBy'
[耗时]:0.0558ms


时间:2025-08-04 15:00:47.433
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_menu_CreateBy ON `sys_menu`(`create_by` Asc)
[耗时]:0.1594ms


时间:2025-08-04 15:00:47.433
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_menu_IsDeleted'
[耗时]:0.0625ms


时间:2025-08-04 15:00:47.434
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_menu_IsDeleted ON `sys_menu`(`is_deleted` Asc)
[耗时]:0.1343ms


时间:2025-08-04 15:00:47.580
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0768ms


时间:2025-08-04 15:00:47.583
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_user"(
"id" bigint NOT NULL PRIMARY KEY ,
"username" varchar(255) NOT NULL  ,
"nick_name" varchar(255) NULL  ,
"email" varchar(255) NULL  ,
"is_admin" bit NULL  ,
"enabled" bit NULL  ,
"password" varchar(255) NOT NULL  ,
"dept_id" bigint NOT NULL  ,
"phone" varchar(11) NULL  ,
"avatar_path" varchar(255) NULL  ,
"password_re_set_time" datetime NULL  ,
"gender" varchar(255) NOT NULL  ,
"tenant_id" integer NOT NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:1.8728ms


时间:2025-08-04 15:00:47.584
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_user_CreateBy'
[耗时]:0.0626ms


时间:2025-08-04 15:00:47.584
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_user_CreateBy ON `sys_user`(`create_by` Asc)
[耗时]:0.1188ms


时间:2025-08-04 15:00:47.585
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_user_IsDeleted'
[耗时]:0.0372ms


时间:2025-08-04 15:00:47.585
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_user_IsDeleted ON `sys_user`(`is_deleted` Asc)
[耗时]:0.0791ms


时间:2025-08-04 15:00:47.736
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0681ms


时间:2025-08-04 15:00:47.741
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_user_job"(
"user_id" bigint NOT NULL  ,
"job_id" bigint NOT NULL   ,
 Primary key("user_id","job_id") )
[耗时]:2.5254ms


时间:2025-08-04 15:00:47.879
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.1207ms


时间:2025-08-04 15:00:47.885
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_user_role"(
"user_id" bigint NOT NULL  ,
"role_id" bigint NOT NULL   ,
 Primary key("user_id","role_id") )
[耗时]:3.8602ms


时间:2025-08-04 15:00:48.002
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0558ms


时间:2025-08-04 15:00:48.005
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_role"(
"id" bigint NOT NULL PRIMARY KEY ,
"name" varchar(255) NOT NULL  ,
"level" integer NOT NULL  ,
"description" varchar(255) NULL  ,
"data_scope_type" integer NOT NULL  ,
"permission" varchar(20) NOT NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:1.6861ms


时间:2025-08-04 15:00:48.005
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_role_CreateBy'
[耗时]:0.0638ms


时间:2025-08-04 15:00:48.006
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_role_CreateBy ON `sys_role`(`create_by` Asc)
[耗时]:0.1297ms


时间:2025-08-04 15:00:48.006
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_sys_role_IsDeleted'
[耗时]:0.0566ms


时间:2025-08-04 15:00:48.008
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_sys_role_IsDeleted ON `sys_role`(`is_deleted` Asc)
[耗时]:0.1814ms


时间:2025-08-04 15:00:48.145
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0513ms


时间:2025-08-04 15:00:48.151
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_role_apis"(
"role_id" bigint NOT NULL  ,
"apis_id" bigint NOT NULL   ,
 Primary key("role_id","apis_id") )
[耗时]:5.04ms


时间:2025-08-04 15:00:48.289
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0516ms


时间:2025-08-04 15:00:48.293
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_role_dept"(
"role_id" bigint NOT NULL  ,
"dept_id" bigint NOT NULL   ,
 Primary key("role_id","dept_id") )
[耗时]:3.2135ms


时间:2025-08-04 15:00:48.438
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.063ms


时间:2025-08-04 15:00:48.441
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "sys_role_menu"(
"role_id" bigint NOT NULL  ,
"menu_id" bigint NOT NULL   ,
 Primary key("role_id","menu_id") )
[耗时]:1.9499ms


时间:2025-08-04 15:00:48.569
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0857ms


时间:2025-08-04 15:00:48.573
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "email_account"(
"id" bigint NOT NULL PRIMARY KEY ,
"email" varchar(255) NOT NULL  ,
"display_name" varchar(255) NOT NULL  ,
"host" varchar(255) NOT NULL  ,
"port" integer NOT NULL  ,
"username" varchar(255) NOT NULL  ,
"password" varchar(255) NULL  ,
"enable_ssl" bit NOT NULL  ,
"use_default_credentials" bit NOT NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:2.1992ms


时间:2025-08-04 15:00:48.573
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_email_account_CreateBy'
[耗时]:0.0694ms


时间:2025-08-04 15:00:48.574
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_email_account_CreateBy ON `email_account`(`create_by` Asc)
[耗时]:0.2146ms


时间:2025-08-04 15:00:48.575
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_email_account_IsDeleted'
[耗时]:0.0746ms


时间:2025-08-04 15:00:48.575
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_email_account_IsDeleted ON `email_account`(`is_deleted` Asc)
[耗时]:0.1377ms


时间:2025-08-04 15:00:48.948
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:select Name from sqlite_master where type='table' and name<>'sqlite_sequence' order by name;
[耗时]:0.0985ms


时间:2025-08-04 15:00:48.951
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE TABLE IF NOT EXISTS "email_message_template"(
"id" bigint NOT NULL PRIMARY KEY ,
"name" varchar(255) NOT NULL  ,
"bcc_email_addresses" varchar(255) NULL  ,
"subject" varchar(255) NULL  ,
"body" text NOT NULL  ,
"is_active" bit NOT NULL  ,
"email_account_id" bigint NOT NULL  ,
"create_by" varchar(255) NULL  ,
"create_time" datetime NULL  ,
"update_by" varchar(255) NULL  ,
"update_time" datetime NULL  ,
"is_deleted" bit NOT NULL    )
[耗时]:2.1788ms


时间:2025-08-04 15:00:48.951
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_email_message_template_CreateBy'
[耗时]:0.0567ms


时间:2025-08-04 15:00:48.952
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_email_message_template_CreateBy ON `email_message_template`(`create_by` Asc)
[耗时]:0.1305ms


时间:2025-08-04 15:00:48.952
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT count(*) FROM sqlite_master WHERE name = 'index_email_message_template_IsDeleted'
[耗时]:0.0386ms


时间:2025-08-04 15:00:48.953
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:CREATE  INDEX index_email_message_template_IsDeleted ON `email_message_template`(`is_deleted` Asc)
[耗时]:0.0965ms


时间:2025-08-04 15:00:49.377
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_user`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:5.3948ms


时间:2025-08-04 15:00:49.615
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_role`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4671ms


时间:2025-08-04 15:00:49.764
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_menu`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2843ms


时间:2025-08-04 15:00:50.281
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_department`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4869ms


时间:2025-08-04 15:00:50.430
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_job`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2989ms


时间:2025-08-04 15:00:50.572
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_setting`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3244ms


时间:2025-08-04 15:00:50.808
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_dict`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3094ms


时间:2025-08-04 15:00:51.020
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_dict_detail`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2759ms


时间:2025-08-04 15:00:51.211
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_quartz_job`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3019ms


时间:2025-08-04 15:00:51.432
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `email_account`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2948ms


时间:2025-08-04 15:00:51.716
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `email_message_template`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2913ms


时间:2025-08-04 15:00:51.884
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_user_role`     LIMIT 0,1
[耗时]:0.2797ms


时间:2025-08-04 15:00:52.088
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_user_job`     LIMIT 0,1
[耗时]:0.2978ms


时间:2025-08-04 15:00:52.273
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_role_menu`     LIMIT 0,1
[耗时]:0.3079ms


时间:2025-08-04 15:00:52.447
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_apis`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2694ms


时间:2025-08-04 15:00:52.683
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_role_apis`     LIMIT 0,1
[耗时]:0.2592ms


时间:2025-08-04 15:00:52.838
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT 1 FROM `sys_tenant`   WHERE ( `is_deleted` = @IsDeleted0 )   LIMIT 0,1 
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2666ms


时间:2025-08-04 15:00:54.681
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `task_name`,`task_group`,`cron`,`assembly_name`,`class_name`,`description`,`principal`,`alert_email`,`pause_after_failure`,`run_times`,`start_time`,`end_time`,`trigger_type`,`interval_second`,`cycle_run_times`,`is_enable`,`run_params`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_quartz_job`  WHERE ( `is_deleted` = @IsDeleted0 )  
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3331ms


时间:2025-08-04 15:04:00.373
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `access_token`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_token_blacklist`   WHERE ( `access_token` = @AccessToken0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@AccessToken0 [Value]:d345a241a1ac6000 [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3742ms


时间:2025-08-04 15:04:00.414
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3362ms


时间:2025-08-04 15:04:00.429
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:04:00.433
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:04:00.435
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:04:00.437
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:04:00.497
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:04:00.683
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`value`,`enabled`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_setting`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:IsAdminNotAuthentication [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.4056ms


时间:2025-08-04 15:04:00.720
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT  `a`.`url` AS `Url` , `a`.`method` AS `Method`  FROM `sys_user_role` `ur`  ,`sys_role_apis`  `ra` ,`sys_apis`  `a`  WHERE (( `ur`.`role_id` = `ra`.`role_id` ) AND ( `ra`.`apis_id` = `a`.`id` ))  AND ( `ur`.`user_id` = @UserId0 )  AND ( `a`.`is_deleted` = @IsDeleted1 )GROUP BY `a`.`url`,`a`.`method` ORDER BY `a`.`url` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.4754ms


时间:2025-08-04 15:04:01.025
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.2955ms


时间:2025-08-04 15:04:01.027
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:04:01.031
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:04:01.034
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:04:01.035
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:04:01.037
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:04:01.045
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `m`.`permission` FROM `sys_user_role` `ur`  ,`sys_role_menu`  `rm` ,`sys_menu`  `m`  WHERE (( `ur`.`role_id` = `rm`.`role_id` ) AND ( `rm`.`menu_id` = `m`.`id` ))  AND ((( `ur`.`user_id` = @UserId0 ) AND ( `m`.`type` <> @Type1 )) AND ( `m`.`permission` IS NOT NULL ))  AND ( `m`.`is_deleted` = @IsDeleted3 )GROUP BY `m`.`permission` ORDER BY `m`.`permission` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@Type1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[耗时]:0.5008ms


时间:2025-08-04 15:04:01.168
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT  DISTINCT  `m`.`title` AS `Title` , `m`.`path` AS `Path` , `m`.`permission` AS `Permission` , `m`.`i_frame` AS `IFrame` , `m`.`component` AS `Component` , `m`.`component_name` AS `ComponentName` , `m`.`parent_id` AS `ParentId` , `m`.`sort` AS `Sort` , `m`.`icon` AS `Icon` , `m`.`type` AS `Type` , `m`.`is_deleted` AS `IsDeleted` , `m`.`id` AS `Id` , `m`.`create_time` AS `CreateTime` , `m`.`create_by` AS `CreateBy` , `m`.`cache` AS `Cache` , `m`.`hidden` AS `Hidden`  FROM `sys_user_role` `ur`  ,`sys_role_menu`  `rm` ,`sys_menu`  `m`  WHERE (( `ur`.`role_id` = `rm`.`role_id` ) AND ( `rm`.`menu_id` = `m`.`id` ))  AND (( `ur`.`user_id` = @UserId0 ) AND ( `m`.`type` <> @Type1 ))  AND ( `m`.`is_deleted` = @IsDeleted2 )ORDER BY `m`.`sort` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@Type1 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[耗时]:0.3964ms


时间:2025-08-04 15:04:01.198
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`value`,`enabled`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_setting`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:IsAuditLogSaveDB [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3222ms


时间:2025-08-04 15:04:14.458
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `username` = @Username0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Username0 [Value]:apevolo [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3665ms


时间:2025-08-04 15:04:14.913
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `username`,`nick_name`,`email`,`is_admin`,`enabled`,`password`,`dept_id`,`phone`,`avatar_path`,`password_re_set_time`,`gender`,`tenant_id`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_user`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.3412ms


时间:2025-08-04 15:04:14.917
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`parent_id` AS `ParentId`,`sort` AS `Sort`,`enabled` AS `Enabled`,`sub_count` AS `SubCount`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_department`  WHERE   `id` = 163519427764317    
[耗时]:0ms


时间:2025-08-04 15:04:14.918
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`role_id` as bid FROM `sys_user_role`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:04:14.919
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`level` AS `Level`,`description` AS `Description`,`data_scope_type` AS `DataScopeType`,`permission` AS `Permission`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_role`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` = 163519427764302     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:04:14.920
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `user_id` as aid,`job_id` as bid FROM `sys_user_job`  WHERE   `user_id` = 163519427764300    
[耗时]:0ms


时间:2025-08-04 15:04:14.922
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[UnKnown] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name` AS `Name`,`sort` AS `Sort`,`enabled` AS `Enabled`,`create_by` AS `CreateBy`,`create_time` AS `CreateTime`,`update_by` AS `UpdateBy`,`update_time` AS `UpdateTime`,`is_deleted` AS `IsDeleted`,`id` AS `Id` FROM `sys_job`  WHERE  ( `is_deleted` = @IsDeleted0 )   AND   `id` IN (163519427764304,163519427764305)     
[Pars]:
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0ms


时间:2025-08-04 15:04:14.927
所在类:
等级:Information
信息:执行DB--> 操作用户:[] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `m`.`permission` FROM `sys_user_role` `ur`  ,`sys_role_menu`  `rm` ,`sys_menu`  `m`  WHERE (( `ur`.`role_id` = `rm`.`role_id` ) AND ( `rm`.`menu_id` = `m`.`id` ))  AND ((( `ur`.`user_id` = @UserId0 ) AND ( `m`.`type` <> @Type1 )) AND ( `m`.`permission` IS NOT NULL ))  AND ( `m`.`is_deleted` = @IsDeleted3 )GROUP BY `m`.`permission` ORDER BY `m`.`permission` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@Type1 [Value]:1 [Type]:Int32    
[Name]:@IsDeleted3 [Value]:False [Type]:Boolean    
[耗时]:0.3812ms


时间:2025-08-04 15:04:15.194
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT  `a`.`url` AS `Url` , `a`.`method` AS `Method`  FROM `sys_user_role` `ur`  ,`sys_role_apis`  `ra` ,`sys_apis`  `a`  WHERE (( `ur`.`role_id` = `ra`.`role_id` ) AND ( `ra`.`apis_id` = `a`.`id` ))  AND ( `ur`.`user_id` = @UserId0 )  AND ( `a`.`is_deleted` = @IsDeleted1 )GROUP BY `a`.`url`,`a`.`method` ORDER BY `a`.`url` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.4668ms


时间:2025-08-04 15:04:15.205
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT  DISTINCT  `m`.`title` AS `Title` , `m`.`path` AS `Path` , `m`.`permission` AS `Permission` , `m`.`i_frame` AS `IFrame` , `m`.`component` AS `Component` , `m`.`component_name` AS `ComponentName` , `m`.`parent_id` AS `ParentId` , `m`.`sort` AS `Sort` , `m`.`icon` AS `Icon` , `m`.`type` AS `Type` , `m`.`is_deleted` AS `IsDeleted` , `m`.`id` AS `Id` , `m`.`create_time` AS `CreateTime` , `m`.`create_by` AS `CreateBy` , `m`.`cache` AS `Cache` , `m`.`hidden` AS `Hidden`  FROM `sys_user_role` `ur`  ,`sys_role_menu`  `rm` ,`sys_menu`  `m`  WHERE (( `ur`.`role_id` = `rm`.`role_id` ) AND ( `rm`.`menu_id` = `m`.`id` ))  AND (( `ur`.`user_id` = @UserId0 ) AND ( `m`.`type` <> @Type1 ))  AND ( `m`.`is_deleted` = @IsDeleted2 )ORDER BY `m`.`sort` ASC  
[Pars]:
[Name]:@UserId0 [Value]:163519427764300 [Type]:Int64    
[Name]:@Type1 [Value]:3 [Type]:Int32    
[Name]:@IsDeleted2 [Value]:False [Type]:Boolean    
[耗时]:0.3978ms


时间:2025-08-04 15:04:19.857
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_type`,`name`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict`   WHERE ( `name` = @Name0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Name0 [Value]:dept_status [Type]:String    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.4969ms


时间:2025-08-04 15:04:19.863
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `dict_id`,`label`,`value`,`dict_sort`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_dict_detail`  WHERE ( `dict_id` = @DictId0 )  AND ( `is_deleted` = @IsDeleted1 )ORDER BY `dict_sort` ASC  
[Pars]:
[Name]:@DictId0 [Value]:163519427764331 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:1.3531ms


时间:2025-08-04 15:04:19.866
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:4.2265ms


时间:2025-08-04 15:04:19.870
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3344ms


时间:2025-08-04 15:04:26.106
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-07-28 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-04 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3256ms


时间:2025-08-04 15:04:26.108
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-07-28 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-04 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3482ms


时间:2025-08-04 15:04:27.288
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0   AND `create_time` >= @Conditcreate_time1   AND `create_time` <= @Conditcreate_time2     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time1 [Value]:2025-07-28 00:00:00 [Type]:String    
[Name]:@Conditcreate_time2 [Value]:2025-08-04 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4279ms


时间:2025-08-04 15:04:27.290
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0   AND `create_time` >= @Conditcreate_time1   AND `create_time` <= @Conditcreate_time2     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time1 [Value]:2025-07-28 00:00:00 [Type]:String    
[Name]:@Conditcreate_time2 [Value]:2025-08-04 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3589ms


时间:2025-08-04 15:04:29.077
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditenabled0 [Value]:True [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-07-28 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-04 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.4825ms


时间:2025-08-04 15:04:29.079
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditenabled0 [Value]:True [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-07-28 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-04 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3669ms


时间:2025-08-04 15:04:31.005
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0     AND ( `is_deleted` = @IsDeleted0 )  
[Pars]:
[Name]:@Conditparent_id0 [Value]:163519427764312 [Type]:Int64    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.303ms


时间:2025-08-04 15:04:34.373
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE ( `id` = @Id0 )  AND ( `is_deleted` = @IsDeleted1 )  ORDER BY DATETIME('now')    LIMIT 0,1 
[Pars]:
[Name]:@Id0 [Value]:163519427764314 [Type]:Int64    
[Name]:@IsDeleted1 [Value]:False [Type]:Boolean    
[耗时]:0.0802ms


时间:2025-08-04 15:04:34.454
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Update] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:UPDATE `sys_department`  SET
           `name`=@name,`parent_id`=@parent_id,`sort`=@sort,`enabled`=@enabled,`sub_count`=@sub_count,`update_by`=@update_by,`update_time`=@update_time  WHERE `id`=@id 
[Pars]:
[Name]:@name [Value]:财务部 [Type]:String    
[Name]:@parent_id [Value]:163519427764312 [Type]:Int64    
[Name]:@sort [Value]:3 [Type]:Int32    
[Name]:@enabled [Value]:False [Type]:Boolean    
[Name]:@sub_count [Value]:2 [Type]:Int32    
[Name]:@update_by [Value]:apevolo [Type]:String    
[Name]:@update_time [Value]:2025-08-04 15:04:34 [Type]:DateTime    
[Name]:@id [Value]:163519427764314 [Type]:Int64    
[耗时]:4.0513ms


时间:2025-08-04 15:04:35.171
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `parent_id` = @Conditparent_id0   AND `create_time` >= @Conditcreate_time1   AND `create_time` <= @Conditcreate_time2     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time1 [Value]:2025-07-28 00:00:00 [Type]:String    
[Name]:@Conditcreate_time2 [Value]:2025-08-04 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.5716ms


时间:2025-08-04 15:04:35.174
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `parent_id` = @Conditparent_id0   AND `create_time` >= @Conditcreate_time1   AND `create_time` <= @Conditcreate_time2     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditparent_id0 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time1 [Value]:2025-07-28 00:00:00 [Type]:String    
[Name]:@Conditcreate_time2 [Value]:2025-08-04 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3587ms


时间:2025-08-04 15:04:36.952
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-07-28 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-04 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.7635ms


时间:2025-08-04 15:04:36.954
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-07-28 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-04 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3292ms


时间:2025-08-04 15:04:46.339
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-08-01 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-05 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3796ms


时间:2025-08-04 15:04:46.341
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-08-01 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-05 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.2725ms


时间:2025-08-04 15:04:53.504
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-08-01 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-05 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3809ms


时间:2025-08-04 15:04:53.506
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-08-01 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-05 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.5458ms


时间:2025-08-04 15:04:57.790
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-08-01 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-05 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3922ms


时间:2025-08-04 15:04:57.792
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-08-01 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-05 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.3526ms


时间:2025-08-04 15:04:59.794
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]: SELECT COUNT(1) FROM (SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`  WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 ) ) CountTable  
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-08-01 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-05 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.5348ms


时间:2025-08-04 15:04:59.796
所在类:
等级:Information
信息:执行DB--> 操作用户:[apevolo] 操作类型:[Query] 数据库ID:[Ape.Volo.Sqlite.Master] 
[Sql]:SELECT `name`,`parent_id`,`sort`,`enabled`,`sub_count`,`create_by`,`create_time`,`update_by`,`update_time`,`is_deleted`,`id` FROM `sys_department`   WHERE   `enabled` = @Conditenabled0   AND `parent_id` = @Conditparent_id1   AND `create_time` >= @Conditcreate_time2   AND `create_time` <= @Conditcreate_time3     AND ( `is_deleted` = @IsDeleted0 )  ORDER BY sort asc LIMIT 0,10 
[Pars]:
[Name]:@Conditenabled0 [Value]:False [Type]:Boolean    
[Name]:@Conditparent_id1 [Value]:0 [Type]:Int64    
[Name]:@Conditcreate_time2 [Value]:2025-08-01 00:00:00 [Type]:String    
[Name]:@Conditcreate_time3 [Value]:2025-08-05 23:59:59 [Type]:String    
[Name]:@IsDeleted0 [Value]:False [Type]:Boolean    
[耗时]:0.433ms

