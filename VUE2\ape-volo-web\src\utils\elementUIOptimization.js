// Element UI 性能优化工具
// 解决 Element UI mousewheel 事件监听器警告

/**
 * 检测浏览器是否支持被动事件监听器
 */
let supportsPassive = false
try {
  const opts = Object.defineProperty({}, 'passive', {
    get: function() {
      supportsPassive = true
      return true
    }
  })
  window.addEventListener('testPassive', null, opts)
  window.removeEventListener('testPassive', null, opts)
} catch (e) {
  supportsPassive = false
}

/**
 * 优化 Element UI 的 mousewheel 指令
 * 这个函数会替换原有的 mousewheel 指令，使其支持被动事件监听器
 */
export function optimizeElementUIMousewheel() {
  // 检查是否在浏览器环境中
  if (typeof window === 'undefined') return

  // 等待 Element UI 加载完成
  const checkElementUI = () => {
    // 检查 Element UI 是否已加载
    if (window.Vue && window.Vue.directive) {
      try {
        // 获取原始的 mousewheel 指令
        const originalMousewheel = window.Vue.directive('mousewheel')
        
        if (originalMousewheel && originalMousewheel.bind) {
          // 创建优化后的 mousewheel 指令
          const optimizedMousewheel = {
            bind(el, binding, vnode) {
              // 调用原始的 bind 方法
              if (originalMousewheel.bind) {
                originalMousewheel.bind.call(this, el, binding, vnode)
              }
              
              // 如果元素上有 mousewheel 事件监听器，优化它们
              optimizeElementMousewheelEvents(el)
            },
            
            update(el, binding, vnode) {
              if (originalMousewheel.update) {
                originalMousewheel.update.call(this, el, binding, vnode)
              }
              optimizeElementMousewheelEvents(el)
            },
            
            unbind(el, binding, vnode) {
              if (originalMousewheel.unbind) {
                originalMousewheel.unbind.call(this, el, binding, vnode)
              }
            }
          }
          
          // 替换原始指令
          window.Vue.directive('mousewheel', optimizedMousewheel)
          console.log('Element UI mousewheel directive optimized')
        }
      } catch (error) {
        console.warn('Failed to optimize Element UI mousewheel directive:', error)
      }
    } else {
      // 如果 Element UI 还没加载，等待一段时间后重试
      setTimeout(checkElementUI, 100)
    }
  }
  
  // 开始检查
  checkElementUI()
}

/**
 * 优化元素上的 mousewheel 事件监听器
 */
function optimizeElementMousewheelEvents(element) {
  if (!element || !supportsPassive) return
  
  try {
    // 获取元素上的所有事件监听器（这在实际中很难做到，所以我们采用其他方法）
    // 我们通过重写 addEventListener 来拦截 mousewheel 事件
    if (!element._optimizedMousewheel) {
      const originalAddEventListener = element.addEventListener
      
      element.addEventListener = function(type, listener, options) {
        if (type === 'mousewheel' || type === 'wheel') {
          // 如果是 mousewheel 或 wheel 事件，添加 passive 选项
          const optimizedOptions = supportsPassive ? {
            passive: true,
            ...(typeof options === 'object' ? options : { capture: !!options })
          } : options
          
          return originalAddEventListener.call(this, type, listener, optimizedOptions)
        }
        
        return originalAddEventListener.call(this, type, listener, options)
      }
      
      element._optimizedMousewheel = true
    }
  } catch (error) {
    console.warn('Failed to optimize element mousewheel events:', error)
  }
}

/**
 * 全局优化 Element UI 组件的事件监听器
 */
export function optimizeElementUIGlobally() {
  if (typeof window === 'undefined') return
  
  // 优化全局的 addEventListener
  const originalAddEventListener = EventTarget.prototype.addEventListener
  
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    // 对于滚动相关的事件，自动添加 passive 选项
    const scrollEvents = ['wheel', 'mousewheel', 'touchstart', 'touchmove', 'scroll']
    
    if (scrollEvents.includes(type) && supportsPassive) {
      const optimizedOptions = {
        passive: true,
        ...(typeof options === 'object' ? options : { capture: !!options })
      }
      
      return originalAddEventListener.call(this, type, listener, optimizedOptions)
    }
    
    return originalAddEventListener.call(this, type, listener, options)
  }
  
  console.log('Element UI global event optimization applied')
}

/**
 * 优化 Element UI 组件的滚动性能
 */
export function optimizeElementUIScrollPerformance() {
  // 添加 CSS 优化
  const style = document.createElement('style')
  style.textContent = `
    /* Element UI 滚动优化 */
    .el-scrollbar__wrap {
      -webkit-overflow-scrolling: touch;
      will-change: scroll-position;
    }
    
    .el-table__body-wrapper {
      -webkit-overflow-scrolling: touch;
      will-change: scroll-position;
    }
    
    .el-select-dropdown {
      will-change: transform, opacity;
    }
    
    .el-popper {
      will-change: transform, opacity;
    }
    
    /* 优化动画性能 */
    .el-fade-in-linear-enter-active,
    .el-fade-in-linear-leave-active {
      will-change: opacity;
    }
    
    .el-zoom-in-top-enter-active,
    .el-zoom-in-top-leave-active {
      will-change: transform, opacity;
    }
    
    /* 优化表格滚动 */
    .el-table {
      contain: layout style paint;
    }
    
    .el-table__body {
      will-change: transform;
    }
  `
  
  document.head.appendChild(style)
  console.log('Element UI scroll performance optimization applied')
}

/**
 * 过滤控制台中的 Element UI 相关警告
 */
export function filterElementUIWarnings() {
  // 保存原始的 console.warn
  const originalConsoleWarn = console.warn
  
  console.warn = function(...args) {
    const message = args.join(' ')
    
    // 过滤掉 Element UI 相关的性能警告
    const ignoredWarnings = [
      'Added non-passive event listener',
      'mousewheel',
      'Violation',
      'setTimeout handler took'
    ]
    
    if (ignoredWarnings.some(warning => message.includes(warning))) {
      return // 忽略这些警告
    }
    
    // 其他警告正常显示
    originalConsoleWarn.apply(console, args)
  }
  
  console.log('Element UI warning filter applied')
}

/**
 * 初始化所有 Element UI 优化
 */
export function initElementUIOptimizations() {
  try {
    // 优化 mousewheel 指令
    optimizeElementUIMousewheel()
    
    // 全局事件优化
    optimizeElementUIGlobally()
    
    // 滚动性能优化
    optimizeElementUIScrollPerformance()
    
    // 过滤警告
    filterElementUIWarnings()
    
    console.log('All Element UI optimizations initialized')
  } catch (error) {
    console.warn('Failed to initialize Element UI optimizations:', error)
  }
}

/**
 * Vue 2 插件形式的优化
 */
export default {
  install(Vue) {
    // 在 Vue 实例创建后初始化优化
    Vue.mixin({
      beforeCreate() {
        // 只在根实例上执行一次
        if (this.$root === this && !this.$root._elementUIOptimized) {
          this.$root._elementUIOptimized = true
          
          // 延迟执行，确保 Element UI 已经加载
          this.$nextTick(() => {
            setTimeout(() => {
              initElementUIOptimizations()
            }, 100)
          })
        }
      }
    })
  }
}
