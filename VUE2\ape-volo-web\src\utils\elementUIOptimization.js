// Element UI 性能优化工具
// 真正修复 Element UI mousewheel 事件监听器警告

import normalizeWheel from 'normalize-wheel'

/**
 * 检测浏览器是否支持被动事件监听器
 */
let supportsPassive = false
try {
  const opts = Object.defineProperty({}, 'passive', {
    get: function() {
      supportsPassive = true
      return true
    }
  })
  window.addEventListener('testPassive', null, opts)
  window.removeEventListener('testPassive', null, opts)
} catch (e) {
  supportsPassive = false
}

/**
 * 修复后的 mousewheel 函数，支持被动事件监听器
 */
function fixedMousewheel(element, callback) {
  if (element && element.addEventListener) {
    const isFirefox = typeof navigator !== 'undefined' && navigator.userAgent.toLowerCase().indexOf('firefox') > -1
    const eventName = isFirefox ? 'DOMMouseScroll' : 'mousewheel'

    const handler = function(event) {
      const normalized = normalizeWheel(event)
      callback && callback.apply(this, [event, normalized])
    }

    // 使用被动事件监听器
    const options = supportsPassive ? { passive: true } : false
    element.addEventListener(eventName, handler, options)

    // 返回清理函数
    return () => {
      element.removeEventListener(eventName, handler, options)
    }
  }
  return null
}

/**
 * 修复后的 mousewheel 指令
 */
const fixedMousewheelDirective = {
  bind(el, binding) {
    // 存储清理函数
    el._mousewheelCleanup = fixedMousewheel(el, binding.value)
  },

  update(el, binding) {
    // 清理旧的监听器
    if (el._mousewheelCleanup) {
      el._mousewheelCleanup()
    }
    // 添加新的监听器
    el._mousewheelCleanup = fixedMousewheel(el, binding.value)
  },

  unbind(el) {
    // 清理监听器
    if (el._mousewheelCleanup) {
      el._mousewheelCleanup()
      el._mousewheelCleanup = null
    }
  }
}

/**
 * 真正修复 Element UI 的 mousewheel 指令
 * 这个函数会替换原有的 mousewheel 指令，使其支持被动事件监听器
 */
export function fixElementUIMousewheel(Vue) {
  if (!Vue || !Vue.directive) {
    console.warn('Vue is not available for mousewheel directive fix')
    return
  }

  try {
    // 直接替换 mousewheel 指令为修复后的版本
    Vue.directive('mousewheel', fixedMousewheelDirective)
    console.log('✅ Element UI mousewheel directive fixed with passive listeners')
  } catch (error) {
    console.error('❌ Failed to fix Element UI mousewheel directive:', error)
  }
}

/**
 * 修复其他可能的滚动事件性能问题
 */
function fixScrollEvents() {
  if (typeof window === 'undefined' || !supportsPassive) return

  // 保存原始的 addEventListener
  const originalAddEventListener = EventTarget.prototype.addEventListener

  // 重写 addEventListener 以自动为滚动事件添加 passive 选项
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    const scrollEvents = ['wheel', 'mousewheel', 'DOMMouseScroll', 'touchstart', 'touchmove']

    if (scrollEvents.includes(type)) {
      // 为滚动相关事件自动添加 passive 选项
      let newOptions = options

      if (typeof options === 'boolean') {
        newOptions = { capture: options, passive: true }
      } else if (typeof options === 'object' && options !== null) {
        newOptions = { ...options, passive: true }
      } else {
        newOptions = { passive: true }
      }

      return originalAddEventListener.call(this, type, listener, newOptions)
    }

    return originalAddEventListener.call(this, type, listener, options)
  }

  console.log('✅ Global scroll events optimized with passive listeners')
}

/**
 * 修复 Element UI 表格滚动性能问题
 */
function fixElementUITableScroll() {
  // 等待 DOM 加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fixElementUITableScroll)
    return
  }

  // 为 Element UI 表格添加性能优化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === 1) { // Element node
          // 优化表格滚动
          const tables = node.querySelectorAll ? node.querySelectorAll('.el-table__body-wrapper') : []
          tables.forEach(table => {
            if (!table._scrollOptimized) {
              table.style.willChange = 'scroll-position'
              table.style.webkitOverflowScrolling = 'touch'
              table._scrollOptimized = true
            }
          })

          // 优化下拉框
          const dropdowns = node.querySelectorAll ? node.querySelectorAll('.el-select-dropdown') : []
          dropdowns.forEach(dropdown => {
            if (!dropdown._dropdownOptimized) {
              dropdown.style.willChange = 'transform, opacity'
              dropdown._dropdownOptimized = true
            }
          })
        }
      })
    })
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true
  })

  console.log('✅ Element UI table scroll performance optimized')
}

/**
 * 添加 Element UI 性能优化 CSS
 */
function addElementUIPerformanceCSS() {
  // 检查是否已经添加过
  if (document.querySelector('#element-ui-performance-css')) {
    return
  }

  const style = document.createElement('style')
  style.id = 'element-ui-performance-css'
  style.textContent = `
    /* Element UI 滚动性能优化 */
    .el-scrollbar__wrap {
      -webkit-overflow-scrolling: touch;
      will-change: scroll-position;
      contain: layout style paint;
    }

    .el-table__body-wrapper {
      -webkit-overflow-scrolling: touch;
      will-change: scroll-position;
      contain: layout style paint;
    }

    .el-select-dropdown {
      will-change: transform, opacity;
      contain: layout style paint;
    }

    .el-popper {
      will-change: transform, opacity;
      contain: layout style paint;
    }

    /* 优化动画性能 */
    .el-fade-in-linear-enter-active,
    .el-fade-in-linear-leave-active {
      will-change: opacity;
    }

    .el-zoom-in-top-enter-active,
    .el-zoom-in-top-leave-active {
      will-change: transform, opacity;
    }

    /* 优化表格性能 */
    .el-table {
      contain: layout style paint;
    }

    .el-table__body {
      will-change: transform;
    }

    /* 优化输入框性能 */
    .el-input__inner {
      will-change: auto;
    }

    /* 优化按钮动画 */
    .el-button {
      will-change: auto;
    }

    .el-button:hover,
    .el-button:focus {
      will-change: background-color, border-color, color;
    }
  `

  document.head.appendChild(style)
  console.log('✅ Element UI performance CSS added')
}

/**
 * 初始化所有 Element UI 真正的修复
 */
export function initElementUIFixes(Vue) {
  if (typeof window === 'undefined') return

  try {
    console.log('🔧 Initializing Element UI performance fixes...')

    // 1. 修复 mousewheel 指令
    fixElementUIMousewheel(Vue)

    // 2. 修复全局滚动事件
    fixScrollEvents()

    // 3. 添加性能优化 CSS
    addElementUIPerformanceCSS()

    // 4. 修复表格滚动性能
    fixElementUITableScroll()

    console.log('✅ All Element UI performance fixes applied successfully!')
  } catch (error) {
    console.error('❌ Failed to apply Element UI fixes:', error)
  }
}

/**
 * 检查修复效果
 */
export function checkFixStatus() {
  const checks = []

  // 检查 mousewheel 指令是否已修复
  if (window.Vue && window.Vue.directive && window.Vue.directive('mousewheel')) {
    checks.push('✅ Mousewheel directive fixed')
  } else {
    checks.push('❌ Mousewheel directive not found')
  }

  // 检查 CSS 是否已添加
  if (document.querySelector('#element-ui-performance-css')) {
    checks.push('✅ Performance CSS added')
  } else {
    checks.push('❌ Performance CSS not found')
  }

  // 检查被动事件监听器支持
  if (supportsPassive) {
    checks.push('✅ Passive event listeners supported')
  } else {
    checks.push('⚠️ Passive event listeners not supported')
  }

  console.log('Element UI Fix Status:')
  checks.forEach(check => console.log(check))

  return checks
}

/**
 * Vue 2 插件形式的真正修复
 */
export default {
  install(Vue) {
    // 立即修复 mousewheel 指令
    initElementUIFixes(Vue)

    // 在根组件创建时进行额外检查
    Vue.mixin({
      beforeCreate() {
        // 只在根实例上执行一次检查
        if (this.$root === this && !this.$root._elementUIFixed) {
          this.$root._elementUIFixed = true

          // 延迟检查修复状态
          this.$nextTick(() => {
            setTimeout(() => {
              checkFixStatus()
            }, 1000)
          })
        }
      }
    })

    // 添加全局方法
    Vue.prototype.$checkElementUIFixes = checkFixStatus
  }
}
