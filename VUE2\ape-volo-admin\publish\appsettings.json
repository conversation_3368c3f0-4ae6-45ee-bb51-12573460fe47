{"urls": "http://*:8002", "AllowedHosts": "*", "JwtAuth": {"Audience": "http://localhost", "Issuer": "http://localhost", "SecurityKey": "5ixKD0BkJxYYroZTvdPs3w9NWRoiUacN", "Expires": 12, "RefreshTokenExpires": 168, "LoginPath": "/auth/login"}, "System": {"IsInitTable": true, "IsInitData": true, "IsCqrs": false, "IsQuickDebug": true, "UserDefaultPassword": "******", "FileLimitSize": 10, "HmacSecret": "z2sCIB2PDuXNcgLRDKvACkL89VgH3iRw", "MasterDataBase": "Ape.Volo.Sqlite.Master", "LogDataBase": "Ape.Volo.Log", "UseRedisCache": false}, "Tenant": {"Enabled": false, "Type": 2}, "DataConnection": {"ConnectionItem": [{"ConnId": "Ape.Volo.Mysql.Master", "HitRate": 100, "DBType": 0, "Enabled": false, "ConnectionString": "Server=localhost; Port=3306;Stmt=; Database=apevoloMaster; Uid=root; Pwd=******"}, {"ConnId": "Ape.Volo.Mysql.Slave", "HitRate": 20, "DBType": 0, "Enabled": false, "ConnectionString": "Server=************; Port=3306;Stmt=; Database=apevoloSlave1; Uid=root; Pwd=******;"}, {"ConnId": "Ape.Volo.Mysql.Slave", "HitRate": 10, "DBType": 0, "Enabled": false, "ConnectionString": "Server=************; Port=3306;Stmt=; Database=apevoloSlave2; Uid=root; Pwd=******;"}, {"ConnId": "Ape.Volo.SqlServer.Master", "HitRate": 20, "DBType": 1, "Enabled": false, "ConnectionString": "Data Source=localhost;User Id = sa;Password = ******;Initial Catalog=apevoloMaster;MultipleActiveResultSets=True;"}, {"ConnId": "Ape.Volo.Sqlite.Master", "HitRate": 0, "DBType": 2, "Enabled": true, "ConnectionString": "apevoloMaster.db"}, {"ConnId": "Ape.Volo.Oracle.Master", "HitRate": 20, "DBType": 3, "Enabled": false, "ConnectionString": "Data Source=localhost/orcl;User ID=system;Password=******;Persist Security Info=True;Connection Timeout=60;"}, {"ConnId": "Ape.Volo.Log", "HitRate": 0, "DBType": 2, "Enabled": true, "ConnectionString": "apevoloLog.db"}]}, "Serilog": {"RecordSqlEnabled": true, "ToDb": {"Enabled": true}, "ToFile": {"Enabled": true}, "ToConsole": {"Enabled": true}, "ToElasticsearch": {"Enabled": false}}, "EventBus": {"Enabled": false, "SubscriptionClientName": "Ape.Volo"}, "Rabbit": {"Connection": "localhost", "Username": "Ape.Volo", "Password": "******", "RetryCount": 3}, "Middleware": {"QuartzNetJob": {"Enabled": true}, "IpLimit": {"Enabled": true}, "MiniProfiler": {"Enabled": true}, "RabbitMq": {"Enabled": false}, "RedisMq": {"Enabled": false}, "Elasticsearch": {"Enabled": false}}, "Aop": {"Tran": {"Enabled": true}, "Cache": {"Enabled": true}}, "Cors": {"Name": "CorsIpAccess", "EnableAll": false, "Policy": [{"Name": "<PERSON><PERSON><PERSON>", "Domain": "http://127.0.0.1:8001"}, {"Name": "<PERSON><PERSON><PERSON>", "Domain": "http://localhost:8001"}]}, "Redis": {"Name": "", "Host": "localhost", "Port": 6379, "Password": "", "Index": 0, "ConnectTimeout": 10000, "SyncTimeout": 10000, "KeepAlive": 20, "ConnectRetry": 10, "AbortOnConnectFail": true, "AllowAdmin": true, "SuspendTime": 10000, "IntervalTime": 0, "MaxQueueConsumption": 100, "ShowLog": false}, "Rsa": {"PrivateKey": "MIICXAIBAAKBgQCAYjJNTDWSTJ8RBnZ9hp7AS8eFJvxpuZYTqGjD8qak45DKkAhp\ni7SNXDNbfDXNbUFeH465hwQIznS5fHdWgUAoqByGjHoYu1jRhDx72EgKhuvDQ3sx\nwLJr0Ynx1f1Ny9yt8wtLaVc9KcJ8m/pHmmAGz+xf7rzZ8dJvnee+OHMkNwIDAQAB\nAoGADt3lhPr8FybP+BOUrsDjQFG7gelRAIM7uadeXmvf1/ym5zYaVfKWJBUsY1M1\nExwBnlSEMsAg+6Nrc+anFnmILsulPGdrSm3fwPKwhzlrwXd0zl3AK3Mz8GK08CIn\nY6KGM0z0tQ1CIC+b+ilMUjquhOAyS041f2FbN7FEXLoS/tUCQQCEDckdx0syO5em\nYSdVtQrCumrH0ZRWHr2QOHXX/0Srz5rPyHcgfjOr0oi3aPAY9zduBcaWswKGjqvQ\ngn+QRYQbAkEA+OKCeACCpPQ5jb+9cRLNpO+rUnDDe1zItSTiZ+qljla34BgbZiZJ\nMW1N1KnesXVUFWg1q6UbtCa5ruNujabKFQJAJn3aNKrl2VOThbTmAU6PP2mVPH9z\njJLTUJmUiAxpWOdfydc2GiuPJjNtFIErEyvkZVabp0uwncYygMm5IzWQJwJBAL6X\nXwgcg+9kIyrcuWr81kRHUTApsFko/X5Sh5rCgxOUbu0KKPk5unVCWgbQKvxEeCaI\njgkqwCNeQHWgLsoE/t0CQEHHJG3Djcl9Ok6N6emZunGoBP83+iPlQ1aoq+K6tXBO\nTDHkswl7VTyfphhDfMDrgX9e3B+6HvswxqvgU+ATZbA=", "PublicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCAYjJNTDWSTJ8RBnZ9hp7AS8eF\nJvxpuZYTqGjD8qak45DKkAhpi7SNXDNbfDXNbUFeH465hwQIznS5fHdWgUAoqByG\njHoYu1jRhDx72EgKhuvDQ3sxwLJr0Ynx1f1Ny9yt8wtLaVc9KcJ8m/pHmmAGz+xf\n7rzZ8dJvnee+OHMkNwIDAQAB"}, "Swagger": {"Enabled": true, "Name": "v1", "Version": "1.0.0", "Title": "Ape.Volo Api 文档"}, "Captcha": {"KeyLength": 0, "ImgWidth": 111, "ImgHeight": 36, "FontSize": 25, "Threshold": 0, "TimeOut": 3600}, "LoginFailedLimit": {"Enabled": true, "MaxAttempts": 3, "Lockout": 7200}}