{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\utils\\elementUIOptimization.js", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\utils\\elementUIOptimization.js", "mtime": 1754291902233}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\babel.config.js", "mtime": 1754278696306}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754288923956}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\eslint-loader\\index.js", "mtime": 1754288859365}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.checkFixStatus = checkFixStatus;\nexports.default = void 0;\nexports.fixElementUIMousewheel = fixElementUIMousewheel;\nexports.initElementUIFixes = initElementUIFixes;\nrequire(\"core-js/modules/web.dom.iterable\");\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nvar _typeof2 = _interopRequireDefault(require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/typeof.js\"));\nrequire(\"core-js/modules/es7.array.includes\");\nvar _normalizeWheel = _interopRequireDefault(require(\"normalize-wheel\"));\n// Element UI 性能优化工具\n// 真正修复 Element UI mousewheel 事件监听器警告\n\n/**\n * 检测浏览器是否支持被动事件监听器\n */\nvar supportsPassive = false;\ntry {\n  var opts = Object.defineProperty({}, 'passive', {\n    get: function get() {\n      supportsPassive = true;\n      return true;\n    }\n  });\n  window.addEventListener('testPassive', null, opts);\n  window.removeEventListener('testPassive', null, opts);\n} catch (e) {\n  supportsPassive = false;\n}\n\n/**\n * 修复后的 mousewheel 函数，支持被动事件监听器\n */\nfunction fixedMousewheel(element, callback) {\n  if (element && element.addEventListener) {\n    var isFirefox = typeof navigator !== 'undefined' && navigator.userAgent.toLowerCase().indexOf('firefox') > -1;\n    var eventName = isFirefox ? 'DOMMouseScroll' : 'mousewheel';\n    var handler = function handler(event) {\n      var normalized = (0, _normalizeWheel.default)(event);\n      callback && callback.apply(this, [event, normalized]);\n    };\n\n    // 使用被动事件监听器\n    var options = supportsPassive ? {\n      passive: true\n    } : false;\n    element.addEventListener(eventName, handler, options);\n\n    // 返回清理函数\n    return function () {\n      element.removeEventListener(eventName, handler, options);\n    };\n  }\n  return null;\n}\n\n/**\n * 修复后的 mousewheel 指令\n */\nvar fixedMousewheelDirective = {\n  bind: function bind(el, binding) {\n    // 存储清理函数\n    el._mousewheelCleanup = fixedMousewheel(el, binding.value);\n  },\n  update: function update(el, binding) {\n    // 清理旧的监听器\n    if (el._mousewheelCleanup) {\n      el._mousewheelCleanup();\n    }\n    // 添加新的监听器\n    el._mousewheelCleanup = fixedMousewheel(el, binding.value);\n  },\n  unbind: function unbind(el) {\n    // 清理监听器\n    if (el._mousewheelCleanup) {\n      el._mousewheelCleanup();\n      el._mousewheelCleanup = null;\n    }\n  }\n};\n\n/**\n * 真正修复 Element UI 的 mousewheel 指令\n * 这个函数会替换原有的 mousewheel 指令，使其支持被动事件监听器\n */\nfunction fixElementUIMousewheel(Vue) {\n  if (!Vue || !Vue.directive) {\n    console.warn('Vue is not available for mousewheel directive fix');\n    return;\n  }\n  try {\n    // 直接替换 mousewheel 指令为修复后的版本\n    Vue.directive('mousewheel', fixedMousewheelDirective);\n    console.log('✅ Element UI mousewheel directive fixed with passive listeners');\n  } catch (error) {\n    console.error('❌ Failed to fix Element UI mousewheel directive:', error);\n  }\n}\n\n/**\n * 修复其他可能的滚动事件性能问题\n */\nfunction fixScrollEvents() {\n  if (typeof window === 'undefined' || !supportsPassive) return;\n\n  // 保存原始的 addEventListener\n  var originalAddEventListener = EventTarget.prototype.addEventListener;\n\n  // 重写 addEventListener 以自动为滚动事件添加 passive 选项\n  EventTarget.prototype.addEventListener = function (type, listener, options) {\n    var scrollEvents = ['wheel', 'mousewheel', 'DOMMouseScroll', 'touchstart', 'touchmove'];\n    if (scrollEvents.includes(type)) {\n      // 为滚动相关事件自动添加 passive 选项\n      var newOptions = options;\n      if (typeof options === 'boolean') {\n        newOptions = {\n          capture: options,\n          passive: true\n        };\n      } else if ((0, _typeof2.default)(options) === 'object' && options !== null) {\n        newOptions = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, options), {}, {\n          passive: true\n        });\n      } else {\n        newOptions = {\n          passive: true\n        };\n      }\n      return originalAddEventListener.call(this, type, listener, newOptions);\n    }\n    return originalAddEventListener.call(this, type, listener, options);\n  };\n  console.log('✅ Global scroll events optimized with passive listeners');\n}\n\n/**\n * 修复 Element UI 表格滚动性能问题\n */\nfunction fixElementUITableScroll() {\n  // 等待 DOM 加载完成后执行\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', fixElementUITableScroll);\n    return;\n  }\n\n  // 为 Element UI 表格添加性能优化\n  var observer = new MutationObserver(function (mutations) {\n    mutations.forEach(function (mutation) {\n      mutation.addedNodes.forEach(function (node) {\n        if (node.nodeType === 1) {\n          // Element node\n          // 优化表格滚动\n          var tables = node.querySelectorAll ? node.querySelectorAll('.el-table__body-wrapper') : [];\n          tables.forEach(function (table) {\n            if (!table._scrollOptimized) {\n              table.style.willChange = 'scroll-position';\n              table.style.webkitOverflowScrolling = 'touch';\n              table._scrollOptimized = true;\n            }\n          });\n\n          // 优化下拉框\n          var dropdowns = node.querySelectorAll ? node.querySelectorAll('.el-select-dropdown') : [];\n          dropdowns.forEach(function (dropdown) {\n            if (!dropdown._dropdownOptimized) {\n              dropdown.style.willChange = 'transform, opacity';\n              dropdown._dropdownOptimized = true;\n            }\n          });\n        }\n      });\n    });\n  });\n  observer.observe(document.body, {\n    childList: true,\n    subtree: true\n  });\n  console.log('✅ Element UI table scroll performance optimized');\n}\n\n/**\n * 添加 Element UI 性能优化 CSS\n */\nfunction addElementUIPerformanceCSS() {\n  // 检查是否已经添加过\n  if (document.querySelector('#element-ui-performance-css')) {\n    return;\n  }\n  var style = document.createElement('style');\n  style.id = 'element-ui-performance-css';\n  style.textContent = \"\\n    /* Element UI \\u6EDA\\u52A8\\u6027\\u80FD\\u4F18\\u5316 */\\n    .el-scrollbar__wrap {\\n      -webkit-overflow-scrolling: touch;\\n      will-change: scroll-position;\\n      contain: layout style paint;\\n    }\\n\\n    .el-table__body-wrapper {\\n      -webkit-overflow-scrolling: touch;\\n      will-change: scroll-position;\\n      contain: layout style paint;\\n    }\\n\\n    .el-select-dropdown {\\n      will-change: transform, opacity;\\n      contain: layout style paint;\\n    }\\n\\n    .el-popper {\\n      will-change: transform, opacity;\\n      contain: layout style paint;\\n    }\\n\\n    /* \\u4F18\\u5316\\u52A8\\u753B\\u6027\\u80FD */\\n    .el-fade-in-linear-enter-active,\\n    .el-fade-in-linear-leave-active {\\n      will-change: opacity;\\n    }\\n\\n    .el-zoom-in-top-enter-active,\\n    .el-zoom-in-top-leave-active {\\n      will-change: transform, opacity;\\n    }\\n\\n    /* \\u4F18\\u5316\\u8868\\u683C\\u6027\\u80FD */\\n    .el-table {\\n      contain: layout style paint;\\n    }\\n\\n    .el-table__body {\\n      will-change: transform;\\n    }\\n\\n    /* \\u4F18\\u5316\\u8F93\\u5165\\u6846\\u6027\\u80FD */\\n    .el-input__inner {\\n      will-change: auto;\\n    }\\n\\n    /* \\u4F18\\u5316\\u6309\\u94AE\\u52A8\\u753B */\\n    .el-button {\\n      will-change: auto;\\n    }\\n\\n    .el-button:hover,\\n    .el-button:focus {\\n      will-change: background-color, border-color, color;\\n    }\\n  \";\n  document.head.appendChild(style);\n  console.log('✅ Element UI performance CSS added');\n}\n\n/**\n * 初始化所有 Element UI 真正的修复\n */\nfunction initElementUIFixes(Vue) {\n  if (typeof window === 'undefined') return;\n  try {\n    console.log('🔧 Initializing Element UI performance fixes...');\n\n    // 1. 修复 mousewheel 指令\n    fixElementUIMousewheel(Vue);\n\n    // 2. 修复全局滚动事件\n    fixScrollEvents();\n\n    // 3. 添加性能优化 CSS\n    addElementUIPerformanceCSS();\n\n    // 4. 修复表格滚动性能\n    fixElementUITableScroll();\n    console.log('✅ All Element UI performance fixes applied successfully!');\n  } catch (error) {\n    console.error('❌ Failed to apply Element UI fixes:', error);\n  }\n}\n\n/**\n * 检查修复效果\n */\nfunction checkFixStatus() {\n  var checks = [];\n\n  // 检查 mousewheel 指令是否已修复\n  if (window.Vue && window.Vue.directive && window.Vue.directive('mousewheel')) {\n    checks.push('✅ Mousewheel directive fixed');\n  } else {\n    checks.push('❌ Mousewheel directive not found');\n  }\n\n  // 检查 CSS 是否已添加\n  if (document.querySelector('#element-ui-performance-css')) {\n    checks.push('✅ Performance CSS added');\n  } else {\n    checks.push('❌ Performance CSS not found');\n  }\n\n  // 检查被动事件监听器支持\n  if (supportsPassive) {\n    checks.push('✅ Passive event listeners supported');\n  } else {\n    checks.push('⚠️ Passive event listeners not supported');\n  }\n  console.log('Element UI Fix Status:');\n  checks.forEach(function (check) {\n    return console.log(check);\n  });\n  return checks;\n}\n\n/**\n * Vue 2 插件形式的真正修复\n */\nvar _default = exports.default = {\n  install: function install(Vue) {\n    // 立即修复 mousewheel 指令\n    initElementUIFixes(Vue);\n\n    // 在根组件创建时进行额外检查\n    Vue.mixin({\n      beforeCreate: function beforeCreate() {\n        // 只在根实例上执行一次检查\n        if (this.$root === this && !this.$root._elementUIFixed) {\n          this.$root._elementUIFixed = true;\n\n          // 延迟检查修复状态\n          this.$nextTick(function () {\n            setTimeout(function () {\n              checkFixStatus();\n            }, 1000);\n          });\n        }\n      }\n    });\n\n    // 添加全局方法\n    Vue.prototype.$checkElementUIFixes = checkFixStatus;\n  }\n};", null]}