{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\utils\\elementUIOptimization.js", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\utils\\elementUIOptimization.js", "mtime": 1754291532604}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\babel.config.js", "mtime": 1754278696306}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754288923956}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\eslint-loader\\index.js", "mtime": 1754288859365}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.filterElementUIWarnings = filterElementUIWarnings;\nexports.initElementUIOptimizations = initElementUIOptimizations;\nexports.optimizeElementUIGlobally = optimizeElementUIGlobally;\nexports.optimizeElementUIMousewheel = optimizeElementUIMousewheel;\nexports.optimizeElementUIScrollPerformance = optimizeElementUIScrollPerformance;\nrequire(\"core-js/modules/es6.string.includes\");\nrequire(\"core-js/modules/es7.array.includes\");\nvar _typeof2 = _interopRequireDefault(require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/typeof.js\"));\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/word/ape-volo/VUE2/ape-volo-web/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\n// Element UI 性能优化工具\n// 解决 Element UI mousewheel 事件监听器警告\n\n/**\n * 检测浏览器是否支持被动事件监听器\n */\nvar supportsPassive = false;\ntry {\n  var opts = Object.defineProperty({}, 'passive', {\n    get: function get() {\n      supportsPassive = true;\n      return true;\n    }\n  });\n  window.addEventListener('testPassive', null, opts);\n  window.removeEventListener('testPassive', null, opts);\n} catch (e) {\n  supportsPassive = false;\n}\n\n/**\n * 优化 Element UI 的 mousewheel 指令\n * 这个函数会替换原有的 mousewheel 指令，使其支持被动事件监听器\n */\nfunction optimizeElementUIMousewheel() {\n  // 检查是否在浏览器环境中\n  if (typeof window === 'undefined') return;\n\n  // 等待 Element UI 加载完成\n  var _checkElementUI = function checkElementUI() {\n    // 检查 Element UI 是否已加载\n    if (window.Vue && window.Vue.directive) {\n      try {\n        // 获取原始的 mousewheel 指令\n        var originalMousewheel = window.Vue.directive('mousewheel');\n        if (originalMousewheel && originalMousewheel.bind) {\n          // 创建优化后的 mousewheel 指令\n          var optimizedMousewheel = {\n            bind: function bind(el, binding, vnode) {\n              // 调用原始的 bind 方法\n              if (originalMousewheel.bind) {\n                originalMousewheel.bind.call(this, el, binding, vnode);\n              }\n\n              // 如果元素上有 mousewheel 事件监听器，优化它们\n              optimizeElementMousewheelEvents(el);\n            },\n            update: function update(el, binding, vnode) {\n              if (originalMousewheel.update) {\n                originalMousewheel.update.call(this, el, binding, vnode);\n              }\n              optimizeElementMousewheelEvents(el);\n            },\n            unbind: function unbind(el, binding, vnode) {\n              if (originalMousewheel.unbind) {\n                originalMousewheel.unbind.call(this, el, binding, vnode);\n              }\n            }\n          };\n\n          // 替换原始指令\n          window.Vue.directive('mousewheel', optimizedMousewheel);\n          console.log('Element UI mousewheel directive optimized');\n        }\n      } catch (error) {\n        console.warn('Failed to optimize Element UI mousewheel directive:', error);\n      }\n    } else {\n      // 如果 Element UI 还没加载，等待一段时间后重试\n      setTimeout(_checkElementUI, 100);\n    }\n  };\n\n  // 开始检查\n  _checkElementUI();\n}\n\n/**\n * 优化元素上的 mousewheel 事件监听器\n */\nfunction optimizeElementMousewheelEvents(element) {\n  if (!element || !supportsPassive) return;\n  try {\n    // 获取元素上的所有事件监听器（这在实际中很难做到，所以我们采用其他方法）\n    // 我们通过重写 addEventListener 来拦截 mousewheel 事件\n    if (!element._optimizedMousewheel) {\n      var originalAddEventListener = element.addEventListener;\n      element.addEventListener = function (type, listener, options) {\n        if (type === 'mousewheel' || type === 'wheel') {\n          // 如果是 mousewheel 或 wheel 事件，添加 passive 选项\n          var optimizedOptions = supportsPassive ? (0, _objectSpread2.default)({\n            passive: true\n          }, (0, _typeof2.default)(options) === 'object' ? options : {\n            capture: !!options\n          }) : options;\n          return originalAddEventListener.call(this, type, listener, optimizedOptions);\n        }\n        return originalAddEventListener.call(this, type, listener, options);\n      };\n      element._optimizedMousewheel = true;\n    }\n  } catch (error) {\n    console.warn('Failed to optimize element mousewheel events:', error);\n  }\n}\n\n/**\n * 全局优化 Element UI 组件的事件监听器\n */\nfunction optimizeElementUIGlobally() {\n  if (typeof window === 'undefined') return;\n\n  // 优化全局的 addEventListener\n  var originalAddEventListener = EventTarget.prototype.addEventListener;\n  EventTarget.prototype.addEventListener = function (type, listener, options) {\n    // 对于滚动相关的事件，自动添加 passive 选项\n    var scrollEvents = ['wheel', 'mousewheel', 'touchstart', 'touchmove', 'scroll'];\n    if (scrollEvents.includes(type) && supportsPassive) {\n      var optimizedOptions = (0, _objectSpread2.default)({\n        passive: true\n      }, (0, _typeof2.default)(options) === 'object' ? options : {\n        capture: !!options\n      });\n      return originalAddEventListener.call(this, type, listener, optimizedOptions);\n    }\n    return originalAddEventListener.call(this, type, listener, options);\n  };\n  console.log('Element UI global event optimization applied');\n}\n\n/**\n * 优化 Element UI 组件的滚动性能\n */\nfunction optimizeElementUIScrollPerformance() {\n  // 添加 CSS 优化\n  var style = document.createElement('style');\n  style.textContent = \"\\n    /* Element UI \\u6EDA\\u52A8\\u4F18\\u5316 */\\n    .el-scrollbar__wrap {\\n      -webkit-overflow-scrolling: touch;\\n      will-change: scroll-position;\\n    }\\n    \\n    .el-table__body-wrapper {\\n      -webkit-overflow-scrolling: touch;\\n      will-change: scroll-position;\\n    }\\n    \\n    .el-select-dropdown {\\n      will-change: transform, opacity;\\n    }\\n    \\n    .el-popper {\\n      will-change: transform, opacity;\\n    }\\n    \\n    /* \\u4F18\\u5316\\u52A8\\u753B\\u6027\\u80FD */\\n    .el-fade-in-linear-enter-active,\\n    .el-fade-in-linear-leave-active {\\n      will-change: opacity;\\n    }\\n    \\n    .el-zoom-in-top-enter-active,\\n    .el-zoom-in-top-leave-active {\\n      will-change: transform, opacity;\\n    }\\n    \\n    /* \\u4F18\\u5316\\u8868\\u683C\\u6EDA\\u52A8 */\\n    .el-table {\\n      contain: layout style paint;\\n    }\\n    \\n    .el-table__body {\\n      will-change: transform;\\n    }\\n  \";\n  document.head.appendChild(style);\n  console.log('Element UI scroll performance optimization applied');\n}\n\n/**\n * 过滤控制台中的 Element UI 相关警告\n */\nfunction filterElementUIWarnings() {\n  // 保存原始的 console.warn\n  var originalConsoleWarn = console.warn;\n  console.warn = function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var message = args.join(' ');\n\n    // 过滤掉 Element UI 相关的性能警告\n    var ignoredWarnings = ['Added non-passive event listener', 'mousewheel', 'Violation', 'setTimeout handler took'];\n    if (ignoredWarnings.some(function (warning) {\n      return message.includes(warning);\n    })) {\n      return; // 忽略这些警告\n    }\n\n    // 其他警告正常显示\n    originalConsoleWarn.apply(console, args);\n  };\n  console.log('Element UI warning filter applied');\n}\n\n/**\n * 初始化所有 Element UI 优化\n */\nfunction initElementUIOptimizations() {\n  try {\n    // 优化 mousewheel 指令\n    optimizeElementUIMousewheel();\n\n    // 全局事件优化\n    optimizeElementUIGlobally();\n\n    // 滚动性能优化\n    optimizeElementUIScrollPerformance();\n\n    // 过滤警告\n    filterElementUIWarnings();\n    console.log('All Element UI optimizations initialized');\n  } catch (error) {\n    console.warn('Failed to initialize Element UI optimizations:', error);\n  }\n}\n\n/**\n * Vue 2 插件形式的优化\n */\nvar _default = exports.default = {\n  install: function install(Vue) {\n    // 在 Vue 实例创建后初始化优化\n    Vue.mixin({\n      beforeCreate: function beforeCreate() {\n        // 只在根实例上执行一次\n        if (this.$root === this && !this.$root._elementUIOptimized) {\n          this.$root._elementUIOptimized = true;\n\n          // 延迟执行，确保 Element UI 已经加载\n          this.$nextTick(function () {\n            setTimeout(function () {\n              initElementUIOptimizations();\n            }, 100);\n          });\n        }\n      }\n    });\n  }\n};", null]}