{"remainingRequest": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\test\\MousewheelTest.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\src\\views\\test\\MousewheelTest.vue", "mtime": 1754291968172}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\babel.config.js", "mtime": 1754278696306}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754288923956}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754288914742}, {"path": "D:\\word\\ape-volo\\VUE2\\ape-volo-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754288946893}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es7.string.pad-start\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'MousewheelTest',\n  data: function data() {\n    return {\n      selectedValue: '',\n      selectedDate: '',\n      fixStatus: [],\n      tableData: [],\n      options: [{\n        value: '1',\n        label: '选项1'\n      }, {\n        value: '2',\n        label: '选项2'\n      }, {\n        value: '3',\n        label: '选项3'\n      }, {\n        value: '4',\n        label: '选项4'\n      }, {\n        value: '5',\n        label: '选项5'\n      }, {\n        value: '6',\n        label: '选项6'\n      }, {\n        value: '7',\n        label: '选项7'\n      }, {\n        value: '8',\n        label: '选项8'\n      }, {\n        value: '9',\n        label: '选项9'\n      }, {\n        value: '10',\n        label: '选项10'\n      }]\n    };\n  },\n  mounted: function mounted() {\n    this.generateTableData();\n    this.checkStatus();\n  },\n  methods: {\n    generateTableData: function generateTableData() {\n      // 生成大量表格数据用于测试滚动\n      for (var i = 0; i < 100; i++) {\n        this.tableData.push({\n          date: \"2024-01-\".concat(String(i % 30 + 1).padStart(2, '0')),\n          name: \"\\u7528\\u6237\".concat(i + 1),\n          address: \"\\u5730\\u5740\".concat(i + 1, \" - \\u8FD9\\u662F\\u4E00\\u4E2A\\u5F88\\u957F\\u7684\\u5730\\u5740\\u7528\\u4E8E\\u6D4B\\u8BD5\\u8868\\u683C\\u6EDA\\u52A8\\u6027\\u80FD\")\n        });\n      }\n    },\n    checkStatus: function checkStatus() {\n      // 检查修复状态\n      if (this.$checkElementUIFixes) {\n        this.fixStatus = this.$checkElementUIFixes();\n      } else {\n        this.fixStatus = ['❌ 修复检查方法不可用'];\n      }\n    }\n  }\n};", null]}