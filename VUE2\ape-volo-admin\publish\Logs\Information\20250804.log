﻿
时间:2025-08-04 15:00:54.611
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using object serializer: Quartz.Simpl.BinaryObjectSerializer, Quartz

时间:2025-08-04 15:00:54.625
所在类:Quartz.Core.SchedulerSignalerImpl
等级:Information
信息:Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl

时间:2025-08-04 15:00:54.625
所在类:Quartz.Core.QuartzScheduler
等级:Information
信息:Quartz Scheduler created

时间:2025-08-04 15:00:54.625
所在类:Quartz.Simpl.RAMJobStore
等级:Information
信息:RAMJobStore initialized.

时间:2025-08-04 15:00:54.626
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Quartz Scheduler ******* - 'QuartzScheduler' with instanceId 'NON_CLUSTERED' initialized

时间:2025-08-04 15:00:54.626
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10

时间:2025-08-04 15:00:54.626
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False

时间:2025-08-04 15:00:54.875
所在类:Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager
等级:Information
信息:User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

时间:2025-08-04 15:01:00.083
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Now listening on: "http://[::]:8002"

时间:2025-08-04 15:01:00.085
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Application started. Press Ctrl+C to shut down.

时间:2025-08-04 15:01:00.085
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Hosting environment: "Production"

时间:2025-08-04 15:01:00.085
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Content root path: "D:\word\ape-volo\VUE2\ape-volo-admin\publish"

时间:2025-08-04 15:03:59.430
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:04:00.778
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:04:00.913
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:01.016
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:01.068
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 44.5897ms.

时间:2025-08-04 15:04:01.073
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:01.083
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 165.4804ms

时间:2025-08-04 15:04:01.084
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:04:01.102
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:04:01.104
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 1483.4390 ms

时间:2025-08-04 15:04:01.108
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 1683.7139ms

时间:2025-08-04 15:04:01.124
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:04:01.136
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:01.158
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:01.195
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 36.8478ms.

时间:2025-08-04 15:04:01.385
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:01.385
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 249.1917ms

时间:2025-08-04 15:04:01.385
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:04:01.387
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 283.6771 ms

时间:2025-08-04 15:04:01.387
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 285.7226ms

时间:2025-08-04 15:04:06.495
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "DELETE" "http"://"localhost:8002""""/auth/logout""" - null 0

时间:2025-08-04 15:04:06.500
所在类:Microsoft.AspNetCore.Cors.Infrastructure.CorsService
等级:Information
信息:CORS policy execution successful.

时间:2025-08-04 15:04:06.503
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)"'

时间:2025-08-04 15:04:06.510
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"Logout\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Logout()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:06.512
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:06.518
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 6.1602ms.

时间:2025-08-04 15:04:06.702
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:06.703
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)" in 192.4268ms

时间:2025-08-04 15:04:06.703
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Logout (Ape.Volo.Api)"'

时间:2025-08-04 15:04:06.703
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "DELETE" "/auth/logout" responded 200 in 207.8354 ms

时间:2025-08-04 15:04:06.703
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "DELETE" "http"://"localhost:8002""""/auth/logout""" - 200 108 "application/json; charset=utf-8" 208.3197ms

时间:2025-08-04 15:04:07.320
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/captcha""" - null null

时间:2025-08-04 15:04:07.354
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)"'

时间:2025-08-04 15:04:07.369
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"Captcha\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Captcha()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:07.373
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:09.496
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 2123.1125ms.

时间:2025-08-04 15:04:09.497
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:09.497
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)" in 2127.3018ms

时间:2025-08-04 15:04:09.497
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)"'

时间:2025-08-04 15:04:09.497
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/captcha" responded 200 in 2150.6127 ms

时间:2025-08-04 15:04:09.497
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/captcha""" - 200 3472 "application/json; charset=utf-8" 2176.9912ms

时间:2025-08-04 15:04:11.425
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "POST" "http"://"localhost:8002""""/auth/login""" - "application/json" 273

时间:2025-08-04 15:04:11.425
所在类:Microsoft.AspNetCore.Cors.Infrastructure.CorsService
等级:Information
信息:CORS policy execution successful.

时间:2025-08-04 15:04:11.426
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)"'

时间:2025-08-04 15:04:11.438
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"Login\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Login(Ape.Volo.SharedModel.Queries.Login.LoginAuthUser)" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:11.469
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:11.480
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 11.4041ms.

时间:2025-08-04 15:04:11.636
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:11.637
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)" in 198.781ms

时间:2025-08-04 15:04:11.638
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)"'

时间:2025-08-04 15:04:11.638
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "POST" "/auth/login" responded 400 in 212.7742 ms

时间:2025-08-04 15:04:11.638
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "POST" "http"://"localhost:8002""""/auth/login""" - 400 121 "application/json; charset=utf-8" 213.3282ms

时间:2025-08-04 15:04:11.671
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/captcha""" - null null

时间:2025-08-04 15:04:11.673
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)"'

时间:2025-08-04 15:04:11.679
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"Captcha\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Captcha()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:11.684
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:11.703
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 18.3537ms.

时间:2025-08-04 15:04:11.703
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:11.703
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)" in 24.1589ms

时间:2025-08-04 15:04:11.703
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Captcha (Ape.Volo.Api)"'

时间:2025-08-04 15:04:11.703
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/captcha" responded 200 in 31.3134 ms

时间:2025-08-04 15:04:11.703
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/captcha""" - 200 3376 "application/json; charset=utf-8" 31.9843ms

时间:2025-08-04 15:04:14.444
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "POST" "http"://"localhost:8002""""/auth/login""" - "application/json" 272

时间:2025-08-04 15:04:14.445
所在类:Microsoft.AspNetCore.Cors.Infrastructure.CorsService
等级:Information
信息:CORS policy execution successful.

时间:2025-08-04 15:04:14.445
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)"'

时间:2025-08-04 15:04:14.448
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"Login\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Login(Ape.Volo.SharedModel.Queries.Login.LoginAuthUser)" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:14.452
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:14.968
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 516.7224ms.

时间:2025-08-04 15:04:15.105
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:15.105
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)" in 656.3523ms

时间:2025-08-04 15:04:15.105
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.Login (Ape.Volo.Api)"'

时间:2025-08-04 15:04:15.105
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "POST" "/auth/login" responded 200 in 660.5882 ms

时间:2025-08-04 15:04:15.105
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "POST" "http"://"localhost:8002""""/auth/login""" - 200 2365 "application/json; charset=utf-8" 661.1252ms

时间:2025-08-04 15:04:15.178
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:04:15.195
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:04:15.202
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:15.202
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:15.207
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 4.4686ms.

时间:2025-08-04 15:04:15.352
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:15.352
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 150.144ms

时间:2025-08-04 15:04:15.352
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:04:15.353
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 173.8988 ms

时间:2025-08-04 15:04:15.353
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 174.4421ms

时间:2025-08-04 15:04:19.719
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=dept_status" - null null

时间:2025-08-04 15:04:19.723
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - null null

时间:2025-08-04 15:04:19.726
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:19.726
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:19.741
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:19.741
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:19.755
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:19.759
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:19.879
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 120.7581ms.

时间:2025-08-04 15:04:19.909
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 154.6331ms.

时间:2025-08-04 15:04:20.038
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:20.038
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 297.0846ms

时间:2025-08-04 15:04:20.038
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:20.039
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 315.0112 ms

时间:2025-08-04 15:04:20.039
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=dept_status" - 200 449 "application/json; charset=utf-8" 319.633ms

时间:2025-08-04 15:04:20.297
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:20.297
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 555.5596ms

时间:2025-08-04 15:04:20.297
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:20.297
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 573.2586 ms

时间:2025-08-04 15:04:20.297
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - 200 304 "application/json; charset=utf-8" 574.5988ms

时间:2025-08-04 15:04:26.088
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59&enabled=false" - null null

时间:2025-08-04 15:04:26.092
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:26.096
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:26.103
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:26.108
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.2822ms.

时间:2025-08-04 15:04:26.704
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:26.705
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 608.1858ms

时间:2025-08-04 15:04:26.705
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:26.705
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 616.1138 ms

时间:2025-08-04 15:04:26.705
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59&enabled=false" - 200 47 "application/json; charset=utf-8" 617.0143ms

时间:2025-08-04 15:04:27.275
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59" - null null

时间:2025-08-04 15:04:27.277
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:27.283
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:27.284
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:27.290
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 6.0845ms.

时间:2025-08-04 15:04:27.440
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:27.441
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 157.7086ms

时间:2025-08-04 15:04:27.441
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:27.441
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 165.4223 ms

时间:2025-08-04 15:04:27.441
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59" - 200 304 "application/json; charset=utf-8" 165.9656ms

时间:2025-08-04 15:04:29.060
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59&enabled=true" - null null

时间:2025-08-04 15:04:29.062
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:29.073
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:29.074
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:29.080
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.8604ms.

时间:2025-08-04 15:04:29.223
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:29.223
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 149.6394ms

时间:2025-08-04 15:04:29.223
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:29.223
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 162.9419 ms

时间:2025-08-04 15:04:29.224
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59&enabled=true" - 200 304 "application/json; charset=utf-8" 163.4782ms

时间:2025-08-04 15:04:30.993
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?parentId=***************" - null null

时间:2025-08-04 15:04:30.995
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:31.000
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:31.002
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:31.005
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 3.1507ms.

时间:2025-08-04 15:04:31.132
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:31.132
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 131.5401ms

时间:2025-08-04 15:04:31.134
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:31.134
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 140.6255 ms

时间:2025-08-04 15:04:31.134
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?parentId=***************" - 200 1110 "application/json; charset=utf-8" 141.102ms

时间:2025-08-04 15:04:34.343
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "PUT" "http"://"localhost:8002""""/api/dept/edit""" - "application/json" 266

时间:2025-08-04 15:04:34.344
所在类:Microsoft.AspNetCore.Cors.Infrastructure.CorsService
等级:Information
信息:CORS policy execution successful.

时间:2025-08-04 15:04:34.346
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Update (Ape.Volo.Api)"'

时间:2025-08-04 15:04:34.355
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Update\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Update(Ape.Volo.SharedModel.Dto.Core.Permission.CreateUpdateDepartmentDto)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:34.363
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Update (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:34.634
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Update (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.NoContentResult" in 271.0152ms.

时间:2025-08-04 15:04:34.796
所在类:Microsoft.AspNetCore.Mvc.StatusCodeResult
等级:Information
信息:Executing StatusCodeResult, setting HTTP status code 204

时间:2025-08-04 15:04:34.797
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Update (Ape.Volo.Api)" in 441.1276ms

时间:2025-08-04 15:04:34.797
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Update (Ape.Volo.Api)"'

时间:2025-08-04 15:04:34.797
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "PUT" "/api/dept/edit" responded 204 in 453.3090 ms

时间:2025-08-04 15:04:34.797
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "PUT" "http"://"localhost:8002""""/api/dept/edit""" - 204 null null 453.8629ms

时间:2025-08-04 15:04:35.144
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59" - null null

时间:2025-08-04 15:04:35.150
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:35.163
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:35.163
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:35.175
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 11.4055ms.

时间:2025-08-04 15:04:35.324
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:35.324
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 161.1882ms

时间:2025-08-04 15:04:35.324
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:35.324
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 180.0699 ms

时间:2025-08-04 15:04:35.325
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59" - 200 304 "application/json; charset=utf-8" 180.8895ms

时间:2025-08-04 15:04:36.943
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59&enabled=false" - null null

时间:2025-08-04 15:04:36.945
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:36.948
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:36.949
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:36.954
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.7608ms.

时间:2025-08-04 15:04:37.086
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:37.086
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 138.1663ms

时间:2025-08-04 15:04:37.086
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:37.087
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 142.9992 ms

时间:2025-08-04 15:04:37.087
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-07-28%2000%3A00%3A00&createTime=2025-08-04%2023%3A59%3A59&enabled=false" - 200 47 "application/json; charset=utf-8" 143.4745ms

时间:2025-08-04 15:04:46.327
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - null null

时间:2025-08-04 15:04:46.329
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:46.335
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:46.336
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:46.341
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 4.6713ms.

时间:2025-08-04 15:04:46.458
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:46.459
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 123.4548ms

时间:2025-08-04 15:04:46.459
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:46.459
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 132.1238 ms

时间:2025-08-04 15:04:46.459
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - 200 47 "application/json; charset=utf-8" 132.5981ms

时间:2025-08-04 15:04:53.490
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - null null

时间:2025-08-04 15:04:53.493
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:53.497
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:53.498
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:53.507
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 9.0145ms.

时间:2025-08-04 15:04:53.678
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:53.679
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 181.4117ms

时间:2025-08-04 15:04:53.679
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:53.679
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 188.4514 ms

时间:2025-08-04 15:04:53.679
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - 200 47 "application/json; charset=utf-8" 189.0896ms

时间:2025-08-04 15:04:57.777
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - null null

时间:2025-08-04 15:04:57.780
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:57.786
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:57.787
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:57.792
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.6818ms.

时间:2025-08-04 15:04:57.920
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:57.920
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 134.6205ms

时间:2025-08-04 15:04:57.921
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:57.921
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 143.0460 ms

时间:2025-08-04 15:04:57.921
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - 200 47 "application/json; charset=utf-8" 143.4501ms

时间:2025-08-04 15:04:59.782
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - null null

时间:2025-08-04 15:04:59.784
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:59.790
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:04:59.791
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:04:59.797
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.5094ms.

时间:2025-08-04 15:04:59.981
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:04:59.981
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 190.8148ms

时间:2025-08-04 15:04:59.981
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:04:59.982
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 199.3187 ms

时间:2025-08-04 15:04:59.982
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&createTime=2025-08-01%2000%3A00%3A00&createTime=2025-08-05%2023%3A59%3A59&enabled=false" - 200 47 "application/json; charset=utf-8" 199.9664ms

时间:2025-08-04 15:05:15.053
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using object serializer: Quartz.Simpl.BinaryObjectSerializer, Quartz

时间:2025-08-04 15:05:15.063
所在类:Quartz.Core.SchedulerSignalerImpl
等级:Information
信息:Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl

时间:2025-08-04 15:05:15.063
所在类:Quartz.Core.QuartzScheduler
等级:Information
信息:Quartz Scheduler created

时间:2025-08-04 15:05:15.064
所在类:Quartz.Simpl.RAMJobStore
等级:Information
信息:RAMJobStore initialized.

时间:2025-08-04 15:05:15.064
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Quartz Scheduler ******* - 'QuartzScheduler' with instanceId 'NON_CLUSTERED' initialized

时间:2025-08-04 15:05:15.064
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10

时间:2025-08-04 15:05:15.064
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False

时间:2025-08-04 15:05:15.268
所在类:Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager
等级:Information
信息:User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

时间:2025-08-04 15:05:15.416
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Now listening on: "http://[::]:8002"

时间:2025-08-04 15:05:15.418
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Application started. Press Ctrl+C to shut down.

时间:2025-08-04 15:05:15.419
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Hosting environment: "Production"

时间:2025-08-04 15:05:15.419
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Content root path: "D:\word\ape-volo\VUE2\ape-volo-admin\publish"

时间:2025-08-04 15:06:43.865
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - null null

时间:2025-08-04 15:06:45.486
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:06:45.609
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.AuthorizationManagement\", action = \"GetInfo\", controller = \"Authorization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetInfo()" on controller "Ape.Volo.Api.Controllers.Auth.AuthorizationController" ("Ape.Volo.Api").

时间:2025-08-04 15:06:45.700
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:06:45.759
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 55.7541ms.

时间:2025-08-04 15:06:45.764
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:06:45.775
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)" in 161.9668ms

时间:2025-08-04 15:06:45.775
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Auth.AuthorizationController.GetInfo (Ape.Volo.Api)"'

时间:2025-08-04 15:06:45.795
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - null null

时间:2025-08-04 15:06:45.825
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/auth/info" responded 200 in 1832.3750 ms

时间:2025-08-04 15:06:45.827
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:06:45.830
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/auth/info""" - 200 1878 "application/json; charset=utf-8" 1995.609ms

时间:2025-08-04 15:06:45.835
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.MenuManagement\", action = \"Build\", controller = \"Menus\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Build()" on controller "Ape.Volo.Api.Controllers.Permission.MenusController" ("Ape.Volo.Api").

时间:2025-08-04 15:06:45.849
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:06:45.880
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 30.6282ms.

时间:2025-08-04 15:06:46.095
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:06:46.096
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)" in 260.7156ms

时间:2025-08-04 15:06:46.096
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.MenusController.Build (Ape.Volo.Api)"'

时间:2025-08-04 15:06:46.097
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/menu/build" responded 200 in 301.2320 ms

时间:2025-08-04 15:06:46.098
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/menu/build""" - 200 4507 "application/json; charset=utf-8" 303.2449ms

时间:2025-08-04 15:06:47.893
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=dept_status" - null null

时间:2025-08-04 15:06:47.895
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - null null

时间:2025-08-04 15:06:47.900
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - null null

时间:2025-08-04 15:06:47.909
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:47.912
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:47.941
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:06:47.948
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DictionaryDetailManagement\", action = \"Query\", controller = \"DictDetail\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.System.DictDetailQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.System.DictDetailController" ("Ape.Volo.Api").

时间:2025-08-04 15:06:47.969
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:06:47.970
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:06:47.996
所在类:Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware
等级:Information
信息:Sending file. Request path: '"/uploads/file/avatar/20231010143458_1711631391147429888.png"'. Physical path: '"D:\word\ape-volo\VUE2\ape-volo-admin\publish\wwwroot\uploads\file\avatar\20231010143458_1711631391147429888.png"'

时间:2025-08-04 15:06:47.996
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/uploads/file/avatar/20231010143458_1711631391147429888.png" responded 200 in 96.0477 ms

时间:2025-08-04 15:06:47.996
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/uploads/file/avatar/20231010143458_1711631391147429888.png""" - 200 76073 "image/png" 96.8201ms

时间:2025-08-04 15:06:48.049
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 79.875ms.

时间:2025-08-04 15:06:48.077
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 106.9677ms.

时间:2025-08-04 15:06:48.170
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:06:48.175
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)" in 226.7232ms

时间:2025-08-04 15:06:48.175
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.System.DictDetailController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:48.176
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dictDetail/query" responded 200 in 273.9022 ms

时间:2025-08-04 15:06:48.176
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dictDetail/query""?dictName=dept_status" - 200 449 "application/json; charset=utf-8" 282.3957ms

时间:2025-08-04 15:06:48.412
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:06:48.413
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 471.4695ms

时间:2025-08-04 15:06:48.413
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:48.413
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 510.7616 ms

时间:2025-08-04 15:06:48.413
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - 200 304 "application/json; charset=utf-8" 518.1696ms

时间:2025-08-04 15:06:52.461
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=false" - null null

时间:2025-08-04 15:06:52.466
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:52.469
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:06:52.471
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:06:52.480
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 9.6798ms.

时间:2025-08-04 15:06:52.611
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:06:52.612
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 141.9931ms

时间:2025-08-04 15:06:52.612
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:52.612
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 150.3712 ms

时间:2025-08-04 15:06:52.612
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=false" - 200 47 "application/json; charset=utf-8" 151.4344ms

时间:2025-08-04 15:06:57.904
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=false" - null null

时间:2025-08-04 15:06:57.908
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:57.914
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:06:57.915
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:06:57.920
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 5.1221ms.

时间:2025-08-04 15:06:58.064
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:06:58.064
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 149.9945ms

时间:2025-08-04 15:06:58.064
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:06:58.064
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 159.5574 ms

时间:2025-08-04 15:06:58.064
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=false" - 200 47 "application/json; charset=utf-8" 160.3001ms

时间:2025-08-04 15:07:02.861
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=true" - null null

时间:2025-08-04 15:07:02.863
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:02.869
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:07:02.869
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:07:02.877
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 7.4514ms.

时间:2025-08-04 15:07:03.028
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:07:03.028
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 159.4309ms

时间:2025-08-04 15:07:03.028
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:03.028
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 167.5754 ms

时间:2025-08-04 15:07:03.029
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc&enabled=true" - 200 304 "application/json; charset=utf-8" 168.0212ms

时间:2025-08-04 15:07:05.086
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?parentId=***************" - null null

时间:2025-08-04 15:07:05.090
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:05.095
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:07:05.095
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:07:05.099
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 3.7849ms.

时间:2025-08-04 15:07:05.216
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:07:05.217
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 121.8482ms

时间:2025-08-04 15:07:05.217
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:05.217
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 130.9886 ms

时间:2025-08-04 15:07:05.217
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?parentId=***************" - 200 1133 "application/json; charset=utf-8" 131.536ms

时间:2025-08-04 15:07:09.666
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - null null

时间:2025-08-04 15:07:09.668
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:09.671
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:07:09.678
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:07:09.683
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 4.3218ms.

时间:2025-08-04 15:07:09.818
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:07:09.819
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 145.8106ms

时间:2025-08-04 15:07:09.819
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:09.819
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 152.8604 ms

时间:2025-08-04 15:07:09.819
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?pageIndex=1&pageSize=10&sortFields=sort%20asc" - 200 304 "application/json; charset=utf-8" 153.2841ms

时间:2025-08-04 15:07:11.499
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?parentId=***************" - null null

时间:2025-08-04 15:07:11.501
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:11.508
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"Query\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Query(Ape.Volo.SharedModel.Queries.Permission.DeptQueryCriteria, Ape.Volo.SharedModel.Queries.Common.Pagination)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:07:11.508
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:07:11.511
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 2.6554ms.

时间:2025-08-04 15:07:11.651
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:07:11.651
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)" in 142.8434ms

时间:2025-08-04 15:07:11.651
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.Query (Ape.Volo.Api)"'

时间:2025-08-04 15:07:11.651
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/query" responded 200 in 151.7537 ms

时间:2025-08-04 15:07:11.651
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/query""?parentId=***************" - 200 1133 "application/json; charset=utf-8" 152.3402ms

时间:2025-08-04 15:07:15.190
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request starting "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/superior""?id=***************" - null null

时间:2025-08-04 15:07:15.193
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executing endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.GetSuperior (Ape.Volo.Api)"'

时间:2025-08-04 15:07:15.200
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Route matched with "{area = \"Area.DepartmentManagement\", action = \"GetSuperior\", controller = \"Dept\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] GetSuperior(Int64)" on controller "Ape.Volo.Api.Controllers.Permission.DeptController" ("Ape.Volo.Api").

时间:2025-08-04 15:07:15.200
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executing action method "Ape.Volo.Api.Controllers.Permission.DeptController.GetSuperior (Ape.Volo.Api)" - Validation state: Valid

时间:2025-08-04 15:07:15.237
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action method "Ape.Volo.Api.Controllers.Permission.DeptController.GetSuperior (Ape.Volo.Api)", returned result "Microsoft.AspNetCore.Mvc.ContentResult" in 36.845ms.

时间:2025-08-04 15:07:15.549
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ContentResultExecutor
等级:Information
信息:Executing ContentResult with HTTP Response ContentType of "application/json; charset=utf-8"

时间:2025-08-04 15:07:15.549
所在类:Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker
等级:Information
信息:Executed action "Ape.Volo.Api.Controllers.Permission.DeptController.GetSuperior (Ape.Volo.Api)" in 348.7819ms

时间:2025-08-04 15:07:15.549
所在类:Microsoft.AspNetCore.Routing.EndpointMiddleware
等级:Information
信息:Executed endpoint '"Ape.Volo.Api.Controllers.Permission.DeptController.GetSuperior (Ape.Volo.Api)"'

时间:2025-08-04 15:07:15.549
所在类:Serilog.AspNetCore.RequestLoggingMiddleware
等级:Information
信息:HTTP "GET" "/api/dept/superior" responded 200 in 358.8743 ms

时间:2025-08-04 15:07:15.549
所在类:Microsoft.AspNetCore.Hosting.Diagnostics
等级:Information
信息:Request finished "HTTP/1.1" "GET" "http"://"localhost:8002""""/api/dept/superior""?id=***************" - 200 1070 "application/json; charset=utf-8" 359.4157ms

时间:2025-08-04 15:08:50.927
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using object serializer: Quartz.Simpl.BinaryObjectSerializer, Quartz

时间:2025-08-04 15:08:50.943
所在类:Quartz.Core.SchedulerSignalerImpl
等级:Information
信息:Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl

时间:2025-08-04 15:08:50.946
所在类:Quartz.Core.QuartzScheduler
等级:Information
信息:Quartz Scheduler created

时间:2025-08-04 15:08:50.948
所在类:Quartz.Simpl.RAMJobStore
等级:Information
信息:RAMJobStore initialized.

时间:2025-08-04 15:08:50.950
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Quartz Scheduler ******* - 'QuartzScheduler' with instanceId 'NON_CLUSTERED' initialized

时间:2025-08-04 15:08:50.951
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10

时间:2025-08-04 15:08:50.953
所在类:Quartz.Impl.StdSchedulerFactory
等级:Information
信息:Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False

时间:2025-08-04 15:08:51.153
所在类:Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager
等级:Information
信息:User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.

时间:2025-08-04 15:08:51.299
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Now listening on: "http://[::]:8002"

时间:2025-08-04 15:08:51.303
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Application started. Press Ctrl+C to shut down.

时间:2025-08-04 15:08:51.305
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Hosting environment: "Production"

时间:2025-08-04 15:08:51.318
所在类:Microsoft.Hosting.Lifetime
等级:Information
信息:Content root path: "D:\word\ape-volo\VUE2\ape-volo-admin\publish"
